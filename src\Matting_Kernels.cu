#include "Matting_Kernels.cuh"
#include <device_launch_parameters.h>
#include <Windows.h>
#include <iostream>

#ifdef __CUDACC__

// CUDA kernel for preprocessing an RGB/RGBA buffer with standard image normalization for inferring
__global__ void PreprocessBufferKernel(float* outputBuffer, const float* inputBuffer, int width, int height, bool isRGBA, NormalizationParams normParams) {
    const int x = blockIdx.x * blockDim.x + threadIdx.x;
    const int y = blockIdx.y * blockDim.y + threadIdx.y;

    if (x < width && y < height) {
        int index = y * width + x;
        int planeSize = width * height;

        // Read pixel from input buffer (RGB or RGBA format)
        int pixelIndex = y * width + x;

        float r = inputBuffer[pixelIndex];                     // R plane
        float g = inputBuffer[pixelIndex + planeSize];         // G plane
        float b = inputBuffer[pixelIndex + 2 * planeSize];     // B plane
        float a = (isRGBA ? inputBuffer[pixelIndex + 3 * planeSize] : 0);  // A plane if exists

        // Normalize and store in output buffer (planar format: all R, then all G, then all B)
        outputBuffer[index] = (r - normParams.meanR) / normParams.stdR;                 // R channel
        outputBuffer[index + planeSize] = (g - normParams.meanG) / normParams.stdG;     // G channel
        outputBuffer[index + 2 * planeSize] = (b - normParams.meanB) / normParams.stdB; // B channel
        if (isRGBA) outputBuffer[index + 3 * planeSize] = a; // A channel
    }
}

// Launcher function for the PreprocessBufferKernel
cudaError_t LaunchPreprocessBufferKernel(float* outputBuffer, const float* inputBuffer, int width, int height, bool isRGBA, const NormalizationParams& normParams, cudaStream_t stream) {
    // Define block and grid dimensions for the kernel launch
    dim3 blockSize(16, 16);
    dim3 gridSize((width + blockSize.x - 1) / blockSize.x, (height + blockSize.y - 1) / blockSize.y);

    if (gridSize.x == 0 || gridSize.y == 0) {
        std::cerr << "Invalid kernel configuration!" << std::endl;
        return cudaErrorInvalidConfiguration;
    }
    // Launch the kernel
    PreprocessBufferKernel<<<gridSize, blockSize, 0, stream>>>(outputBuffer, inputBuffer, width, height, isRGBA, normParams);

    // Check for launch errors
    return cudaGetLastError();
}


#endif // __CUDACC__