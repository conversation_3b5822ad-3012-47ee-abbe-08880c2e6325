// CUDA ProRes 4444 Encoding with AI Alpha Matting - Two-Phase Processing
//
// This application processes video files using a two-phase approach:
// Phase 1: Initial alpha matte generation and compression
// Phase 2: Alpha refinement with IndexNet and background estimation
//
// This approach manages GPU memory constraints more efficiently by freeing
// the initial matting model between phases.

#include <Windows.h>
#include <iostream>
#include <string>
#include <memory>
#include <chrono>
#include <thread>
#include <queue>
#include <mutex>
#include <condition_variable>
#include <atomic>
#include <iomanip>
#include <vector>
#include "cuda_runtime.h"
#include <cuda.h>  // CUDA Driver API
#include "DirectVideoWriterAlphaCuda.h"
#include "DirectVideoReader.h"
#include <Helpers.h>
#include <main_Kernels.cuh>
#include <FrameProcessor.h>
#include <CudaProResEncoder.h>
#include <ImageMattingFactory.h>

// Structure to hold compressed alpha data for each frame
struct FrameAlphaData {
    int frameIndex;
    unsigned char* encodedAlpha;
    size_t encodedSize;
    double timestamp;
};

// CUDA ProRes 4444 encoding with two-phase AI alpha matting
bool TestTwoPhaseProcessing(CUcontext context) {
    std::cout << "\n=== Two-Phase CUDA ProRes 4444 Encoding (Memory Efficient) ===" << std::endl;

    std::string inputPath = "Videos\\input short.mp4";  // Regular video
    std::string outputPath = "Videos\\output_twophase_prores.mov";

    try {
        // Create regular video reader (hardware accelerated for speed)
        auto reader = DirectVideoReader::Create(inputPath, context, true);
        if (!reader) {
            std::cerr << "Failed to create video reader for: " << inputPath << std::endl;
            return false;
        }

        std::cout << "Input video properties:" << std::endl;
        std::cout << "  Dimensions: " << reader->GetWidth() << "x" << reader->GetHeight() << std::endl;
        std::cout << "  Duration: " << reader->GetDuration() << " seconds" << std::endl;
        std::cout << "  Frame rate: " << reader->GetFrameRateDouble() << " fps" << std::endl;

        // Create CUDA stream for processing
        cudaStream_t processingStream;
        CUDA_CHECK(cudaStreamCreate(&processingStream));

        // Allocate buffers for hardware-accelerated video reading (NV12 format)
        int nv12Height = reader->GetHeight() + reader->GetHeight() / 2;
        void* d_inputNv12Array = nullptr;
        size_t inputPitch = 0;
        CUDA_CHECK(cudaMallocPitch(&d_inputNv12Array, &inputPitch, reader->GetWidth(), nv12Height));

        // Vector to store compressed alpha data for all frames
        std::vector<FrameAlphaData> frameAlphaDataList;

        // ===== PHASE 1: Initial Alpha Generation =====
        std::cout << "\n=== PHASE 1: Initial Alpha Generation ===" << std::endl;
        
        // Initialize frame processor for initial matting only
        auto processor = std::make_unique<FrameProcessor>();
        if (!processor->InitializeInitialMatte(reader->GetWidth(), reader->GetHeight(), processingStream)) {
            std::cerr << "Failed to initialize FrameProcessor for initial matting" << std::endl;
            return false;
        }
        std::cout << "FrameProcessor initialized for initial matting" << std::endl;

        // Timing for phase 1
        double totalPhase1Time = 0.0;
        int frameCount = 0;
        double timestamp = 0.0;
        auto phase1StartTime = std::chrono::high_resolution_clock::now();

        // Reset reader to beginning
        reader.reset();
        reader = DirectVideoReader::Create(inputPath, context, true);

        // Process all frames for initial alpha generation
        std::cout << "Generating initial alpha mattes..." << std::endl;
        while (true) {
            auto frameStartTime = std::chrono::high_resolution_clock::now();

            // Read frame
            timestamp = reader->ReadFrame(d_inputNv12Array, inputPitch * nv12Height, inputPitch);
            if (timestamp < 0.0) break;

            // Generate initial alpha and compress it
            unsigned char* encodedAlpha = nullptr;
            size_t encodedSize = 0;
            if (!processor->FindInitialAlpha(d_inputNv12Array, inputPitch, &encodedAlpha, &encodedSize)) {
                std::cerr << "Failed to generate initial alpha for frame " << frameCount << std::endl;
                break;
            }

            // Store compressed alpha data
            FrameAlphaData frameData;
            frameData.frameIndex = frameCount;
            frameData.encodedAlpha = encodedAlpha;
            frameData.encodedSize = encodedSize;
            frameData.timestamp = timestamp;
            frameAlphaDataList.push_back(frameData);

            auto frameEndTime = std::chrono::high_resolution_clock::now();
            double frameTime = std::chrono::duration<double, std::milli>(frameEndTime - frameStartTime).count();
            totalPhase1Time += frameTime;

            frameCount++;

            if (frameCount % 10 == 0) {
                std::cout << "Phase 1: Processed " << frameCount << " frames, avg time: " 
                         << std::fixed << std::setprecision(1) << (totalPhase1Time / frameCount) << " ms/frame" << std::endl;
            }
        }

        auto phase1EndTime = std::chrono::high_resolution_clock::now();
        double phase1Duration = std::chrono::duration<double>(phase1EndTime - phase1StartTime).count();

        std::cout << "Phase 1 completed: " << frameCount << " frames processed in " 
                 << std::fixed << std::setprecision(2) << phase1Duration << " seconds" << std::endl;
        std::cout << "Average Phase 1 time: " << std::fixed << std::setprecision(1) 
                 << (totalPhase1Time / frameCount) << " ms/frame" << std::endl;

        // Clean up initial matting processor to free GPU memory
        processor->Cleanup();
        processor.reset();
        std::cout << "Initial matting processor cleaned up, GPU memory freed" << std::endl;

        // ===== PHASE 2: Alpha Refinement and Final Processing =====
        std::cout << "\n=== PHASE 2: Alpha Refinement and Final Processing ===" << std::endl;

        // Initialize frame processor for refinement
        processor = std::make_unique<FrameProcessor>();
        if (!processor->InitializeRefinement(reader->GetWidth(), reader->GetHeight(), processingStream)) {
            std::cerr << "Failed to initialize FrameProcessor for refinement" << std::endl;
            return false;
        }
        std::cout << "FrameProcessor initialized for refinement" << std::endl;

        // Configure output for CUDA ProRes 4444 with alpha
        DirectVideoWriterAlphaCuda::OutputConfig outputConfig;
        outputConfig.width = reader->GetWidth();
        outputConfig.height = reader->GetHeight();
        outputConfig.frameRate = reader->GetFrameRate();
        outputConfig.outputPath = outputPath;
        outputConfig.UseProRes4444();

        // Create CUDA alpha video writer
        auto writer = DirectVideoWriterAlphaCuda::Create(outputConfig, context);
        if (!writer) {
            std::cerr << "Failed to create CUDA ProRes video writer" << std::endl;
            return false;
        }

        // Allocate buffer for RGBA output
        size_t rgbaBufferSize = reader->GetWidth() * reader->GetHeight() * 4 * sizeof(float);
        void* d_rgbaBuffer = nullptr;
        CUDA_CHECK(cudaMalloc(&d_rgbaBuffer, rgbaBufferSize));

        // Reset reader for second pass
        reader.reset();
        reader = DirectVideoReader::Create(inputPath, context, true);

        // Timing for phase 2
        double totalPhase2Time = 0.0;
        double totalEncodingTime = 0.0;
        auto phase2StartTime = std::chrono::high_resolution_clock::now();

        // Process all frames for refinement and encoding
        std::cout << "Refining alpha mattes and encoding..." << std::endl;
        int currentFrameIndex = 0;
        while (true) {
            auto frameStartTime = std::chrono::high_resolution_clock::now();

            // Read frame
            timestamp = reader->ReadFrame(d_inputNv12Array, inputPitch * nv12Height, inputPitch);
            if (timestamp < 0.0) break;

            // Get corresponding alpha data
            if (currentFrameIndex >= frameAlphaDataList.size()) {
                std::cerr << "Frame index mismatch in phase 2" << std::endl;
                break;
            }

            FrameAlphaData& frameData = frameAlphaDataList[currentFrameIndex];

            // Refine alpha and generate final RGBA
            auto refinementStart = std::chrono::high_resolution_clock::now();
            if (!processor->RefineInitialAlpha(d_inputNv12Array, inputPitch, 
                                             frameData.encodedAlpha, frameData.encodedSize,
                                             static_cast<float*>(d_rgbaBuffer))) {
                std::cerr << "Failed to refine alpha for frame " << currentFrameIndex << std::endl;
                break;
            }
            auto refinementEnd = std::chrono::high_resolution_clock::now();
            double refinementTime = std::chrono::duration<double, std::milli>(refinementEnd - refinementStart).count();

            // Encode frame
            auto encodingStart = std::chrono::high_resolution_clock::now();
            if (!writer->WriteFrame(d_rgbaBuffer, rgbaBufferSize)) {
                std::cerr << "Failed to write frame " << currentFrameIndex << std::endl;
                break;
            }
            auto encodingEnd = std::chrono::high_resolution_clock::now();
            double encodingTime = std::chrono::duration<double, std::milli>(encodingEnd - encodingStart).count();

            totalPhase2Time += refinementTime;
            totalEncodingTime += encodingTime;

            currentFrameIndex++;

            if (currentFrameIndex % 10 == 0) {
                std::cout << "Phase 2: Processed " << currentFrameIndex << " frames, avg refinement: " 
                         << std::fixed << std::setprecision(1) << (totalPhase2Time / currentFrameIndex) 
                         << " ms/frame, avg encoding: " << std::fixed << std::setprecision(1) 
                         << (totalEncodingTime / currentFrameIndex) << " ms/frame" << std::endl;
            }
        }

        auto phase2EndTime = std::chrono::high_resolution_clock::now();
        double phase2Duration = std::chrono::duration<double>(phase2EndTime - phase2StartTime).count();

        std::cout << "Phase 2 completed: " << currentFrameIndex << " frames processed in " 
                 << std::fixed << std::setprecision(2) << phase2Duration << " seconds" << std::endl;

        // Finalize video
        std::cout << "Finalizing CUDA ProRes video..." << std::endl;
        writer->Finalize();

        // Clean up frame processor
        processor->Cleanup();

        // Clean up compressed alpha data
        for (auto& frameData : frameAlphaDataList) {
            if (frameData.encodedAlpha) {
                free(frameData.encodedAlpha);
            }
        }

        // Cleanup buffers
        if (d_inputNv12Array) cudaFree(d_inputNv12Array);
        if (d_rgbaBuffer) cudaFree(d_rgbaBuffer);
        if (processingStream) cudaStreamDestroy(processingStream);

        // Final timing summary
        double totalDuration = phase1Duration + phase2Duration;
        std::cout << "\n=== TWO-PHASE PROCESSING SUMMARY ===" << std::endl;
        std::cout << "Phase 1 (Initial Alpha): " << std::fixed << std::setprecision(2) << phase1Duration << " seconds" << std::endl;
        std::cout << "Phase 2 (Refinement + Encoding): " << std::fixed << std::setprecision(2) << phase2Duration << " seconds" << std::endl;
        std::cout << "Total processing time: " << std::fixed << std::setprecision(2) << totalDuration << " seconds" << std::endl;
        std::cout << "Total frames processed: " << frameCount << std::endl;
        std::cout << "Overall average speed: " << std::fixed << std::setprecision(1) << (frameCount / totalDuration) << " fps" << std::endl;
        std::cout << "\nAverage time per operation:" << std::endl;
        std::cout << "  Phase 1 (Initial Alpha): " << std::fixed << std::setprecision(1) << (totalPhase1Time / frameCount) << " ms/frame" << std::endl;
        std::cout << "  Phase 2 (Refinement): " << std::fixed << std::setprecision(1) << (totalPhase2Time / frameCount) << " ms/frame" << std::endl;
        std::cout << "  ProRes encoding: " << std::fixed << std::setprecision(1) << (totalEncodingTime / frameCount) << " ms/frame" << std::endl;

        std::cout << "\nFinal stats: " << writer->GetFrameCount() << " frames encoded, " << writer->GetTotalBytes() << " bytes" << std::endl;
        std::cout << "Output saved to: " << outputPath << std::endl;
        return true;
    }
    catch (const std::exception& e) {
        std::cerr << "Error in two-phase processing: " << e.what() << std::endl;
        return false;
    }
}

int main(int argc, char* argv[]) {
    std::cout << "=== Two-Phase CUDA ProRes 4444 Encoding with AI Alpha Matting ===" << std::endl;

    // Initialize CUDA driver API
    cuInit(0);

    // Get device and create context explicitly
    CUdevice device;
    CUcontext context;
    cuDeviceGet(&device, 0);
    cuCtxCreate(&context, 0, device);

    // Make it current
    cuCtxSetCurrent(context);

    // Set FFmpeg log level to quiet to reduce overhead
    av_log_set_level(AV_LOG_QUIET);

    try {
        bool success = TestTwoPhaseProcessing(context);

        if (success) {
            std::cout << "\n=== Two-phase processing completed successfully! ===" << std::endl;
        } else {
            std::cout << "\n=== Two-phase processing failed! ===" << std::endl;
            return 1;
        }
    }
    catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }

    // Cleanup CUDA context
    cuCtxDestroy(context);

    return 0;
}
