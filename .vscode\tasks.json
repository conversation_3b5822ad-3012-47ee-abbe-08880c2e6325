{"version": "2.0.0", "tasks": [{"label": "Build Debug", "type": "shell", "command": "${workspaceFolder}/build_debug.bat", "args": [], "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": ["$msCompile", "$nvcc"], "detail": "Build ImageMatter in Debug configuration using batch file"}, {"label": "Build Release", "type": "shell", "command": "${workspaceFolder}/build_release.bat", "args": [], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": ["$msCompile", "$nvcc"], "detail": "Build ImageMatter in Release configuration using batch file"}, {"label": "Clean", "type": "shell", "command": "cmd", "args": ["/c", "if exist x64\\Debug rmdir /s /q x64\\Debug && if exist x64\\Release rmdir /s /q x64\\Release && echo Clean complete!"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": [], "detail": "Clean build artifacts"}, {"label": "Setup Dependencies", "type": "shell", "command": "cmd", "args": ["/c", "echo Setting up dependencies... && if not exist \"x64\\Debug\\ModelsVggHeadDetector\" mkdir \"x64\\Debug\\ModelsVggHeadDetector\" && if not exist \"x64\\Debug\\ModelsInsPyReNet\" mkdir \"x64\\Debug\\ModelsInsPyReNet\" && if not exist \"x64\\Debug\\ModelsIndexNet\" mkdir \"x64\\Debug\\ModelsIndexNet\" && if not exist \"x64\\Debug\\ModelsIsNetDis\" mkdir \"x64\\Debug\\ModelsIsNetDis\" && if not exist \"x64\\Debug\\RMBG1_4\" mkdir \"x64\\Debug\\RMBG1_4\" && if not exist \"x64\\Debug\\RMBG2\" mkdir \"x64\\Debug\\RMBG2\" && echo Dependencies setup complete!"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": [], "detail": "Setup required directories and dependencies"}, {"label": "Post-Build Debug", "type": "shell", "command": "cmd", "args": ["/c", "if not exist \"x64\\Debug\\ModelsVggHeadDetector\" mkdir \"x64\\Debug\\ModelsVggHeadDetector\" && if not exist \"x64\\Debug\\ModelsInsPyReNet\" mkdir \"x64\\Debug\\ModelsInsPyReNet\" && if not exist \"x64\\Debug\\ModelsIndexNet\" mkdir \"x64\\Debug\\ModelsIndexNet\" && if not exist \"x64\\Debug\\ModelsIsNetDis\" mkdir \"x64\\Debug\\ModelsIsNetDis\" && if not exist \"x64\\Debug\\RMBG1_4\" mkdir \"x64\\Debug\\RMBG1_4\" && if not exist \"x64\\Debug\\RMBG2\" mkdir \"x64\\Debug\\RMBG2\" && xcopy /E /I /Y /D \"%VccLibs%\\onnxruntime\\lib\\*.dll\" \"x64\\Debug\" && xcopy /E /I /Y /D \"%VccLibs%\\ffmpeg\\bin\\*.dll\" \"x64\\Debug\" && xcopy /E /I /Y /D \"ModelsVggHeadDetector\\*.onnx\" \"x64\\Debug\\ModelsVggHeadDetector\" && xcopy /E /I /Y /D \"ModelsInsPyReNet\\*.onnx\" \"x64\\Debug\\ModelsInsPyReNet\" && xcopy /E /I /Y /D \"ModelsIndexNet\\*.onnx\" \"x64\\Debug\\ModelsIndexNet\" && xcopy /E /I /Y /D \"ModelsIsNetDis\\*.onnx\" \"x64\\Debug\\ModelsIsNetDis\" && xcopy /E /I /Y /D \"RMBG1_4\\*.onnx\" \"x64\\Debug\\RMBG1_4\" && xcopy /E /I /Y /D \"RMBG2\\*.onnx\" \"x64\\Debug\\RMBG2\" && echo Post-build complete for Debug!"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": [], "detail": "Copy DLLs and model files for Debug configuration"}, {"label": "Post-Build Release", "type": "shell", "command": "cmd", "args": ["/c", "if not exist \"x64\\Release\\ModelsVggHeadDetector\" mkdir \"x64\\Release\\ModelsVggHeadDetector\" && if not exist \"x64\\Release\\ModelsInsPyReNet\" mkdir \"x64\\Release\\ModelsInsPyReNet\" && if not exist \"x64\\Release\\ModelsIndexNet\" mkdir \"x64\\Release\\ModelsIndexNet\" && if not exist \"x64\\Release\\ModelsIsNetDis\" mkdir \"x64\\Release\\ModelsIsNetDis\" && if not exist \"x64\\Release\\RMBG1_4\" mkdir \"x64\\Release\\RMBG1_4\" && if not exist \"x64\\Release\\RMBG2\" mkdir \"x64\\Release\\RMBG2\" && xcopy /E /I /Y /D \"%VccLibs%\\onnxruntime\\lib\\*.dll\" \"x64\\Release\" && xcopy /E /I /Y /D \"%VccLibs%\\ffmpeg\\bin\\*.dll\" \"x64\\Release\" && xcopy /E /I /Y /D \"ModelsVggHeadDetector\\*.onnx\" \"x64\\Release\\ModelsVggHeadDetector\" && xcopy /E /I /Y /D \"ModelsInsPyReNet\\*.onnx\" \"x64\\Release\\ModelsInsPyReNet\" && xcopy /E /I /Y /D \"ModelsIndexNet\\*.onnx\" \"x64\\Release\\ModelsIndexNet\" && xcopy /E /I /Y /D \"ModelsIsNetDis\\*.onnx\" \"x64\\Release\\ModelsIsNetDis\" && xcopy /E /I /Y /D \"RMBG1_4\\*.onnx\" \"x64\\Release\\RMBG1_4\" && xcopy /E /I /Y /D \"RMBG2\\*.onnx\" \"x64\\Release\\RMBG2\" && echo Post-build complete for Release!"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": [], "detail": "Copy DLLs and model files for Release configuration"}, {"label": "Build Debug with Post-Build", "dependsOrder": "sequence", "dependsOn": ["Build Debug", "Post-Build Debug"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": [], "detail": "Build Debug and run post-build actions"}, {"label": "Build Release with Post-Build", "dependsOrder": "sequence", "dependsOn": ["Build Release", "Post-Build Release"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": [], "detail": "Build Release and run post-build actions"}, {"label": "Run Release", "type": "shell", "command": "${workspaceFolder}/x64/Release/ImageMatter.exe", "args": ["4"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": [], "detail": "Run ImageMatter in Release mode (Mode 4: CUDA ProRes)", "dependsOn": "Build Release with Post-Build"}]}