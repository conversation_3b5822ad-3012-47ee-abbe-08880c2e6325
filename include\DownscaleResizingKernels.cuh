#ifndef RESIZE_KERNELS_H
#define RESIZE_KERNELS_H

#include <cuda_runtime.h>

#ifdef __cplusplus
extern "C" {
#endif

    // Lanczos-3 interpolation kernel functions
    cudaError_t LaunchLanczosResizeDownKernel(float* output, int outputWidth, int outputHeight,
        float* input, int inputWidth, int inputHeight,
        int channels);

    cudaError_t LanczosResizeKernelLauncher(float* output, int outputWidth, int outputHeight,
        const float* input, int inputWidth, int inputHeight,
        int channels, cudaStream_t stream);

    // Mitchell-Netravali filter kernel functions
    cudaError_t LaunchMitchellResizeDownKernel(float* output, int outputWidth, int outputHeight,
        float* input, int inputWidth, int inputHeight,
        int channels);

    cudaError_t MitchellResizeKernelLauncher(float* output, int outputWidth, int outputHeight,
        const float* input, int inputWidth, int inputHeight,
        int channels, cudaStream_t stream);

#ifdef __cplusplus
}
#endif

#endif // RESIZE_KERNELS_H