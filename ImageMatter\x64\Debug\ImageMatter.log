﻿  alpha_codec.cpp
F:\Catechese\EditeurAudioVideo\ImageMatter\include\alpha_codec.h(23,4): error C3861: 'free' : identificateur introuvable
  (compiler le fichier source 'src/alpha_codec.cpp')
  
  FrameProcessor.cpp
F:\Catechese\EditeurAudioVideo\ImageMatter\src\FrameProcessor.cpp(386,42): error C2660: 'decompress_alpha_cuda' : la fonction ne prend pas 5 arguments
      F:\Catechese\EditeurAudioVideo\ImageMatter\include\alpha_codec.h(51,13):
      voir la déclaration de 'decompress_alpha_cuda'
      F:\Catechese\EditeurAudioVideo\ImageMatter\src\FrameProcessor.cpp(386,42):
      lors de la tentative de mise en correspondance de la liste des arguments '(unsigned char *, size_t, int, int, cudaStream_t)'
  
F:\Catechese\EditeurAudioVideo\ImageMatter\src\FrameProcessor.cpp(386,40): error C2440: 'initialisation' : impossible de convertir de '_T' en 'unsigned char *'
F:\Catechese\EditeurAudioVideo\ImageMatter\src\FrameProcessor.cpp(386,40): error C2440:         with
F:\Catechese\EditeurAudioVideo\ImageMatter\src\FrameProcessor.cpp(386,40): error C2440:         [
F:\Catechese\EditeurAudioVideo\ImageMatter\src\FrameProcessor.cpp(386,40): error C2440:             _T=cudaError_t
F:\Catechese\EditeurAudioVideo\ImageMatter\src\FrameProcessor.cpp(386,40): error C2440:         ]
      F:\Catechese\EditeurAudioVideo\ImageMatter\src\FrameProcessor.cpp(386,40):
      La conversion d'un type intégral en un type pointeur nécessite un reinterpret_cast, un cast de style C ou un cast de style fonction entre parenthèses.
  
F:\Catechese\EditeurAudioVideo\ImageMatter\src\FrameProcessor.cpp(406,10): error C3861: 'ProcessUncertainRegions' : identificateur introuvable
F:\Catechese\EditeurAudioVideo\ImageMatter\src\FrameProcessor.cpp(414,10): error C3861: 'EstimateBackground' : identificateur introuvable
  main.cpp
  Génération de code en cours...
