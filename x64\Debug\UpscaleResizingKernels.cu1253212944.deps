C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\apiset.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\apisetcconv.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\basetsd.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\bcrypt.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\cderr.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\driverspecs.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\guiddef.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\inaddr.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\kernelspecs.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\ktmtypes.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\minwindef.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\poppack.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\pshpack1.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\pshpack2.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\pshpack4.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\pshpack8.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\rpc.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\rpcasync.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\rpcdce.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\rpcdcep.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\rpcndr.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\rpcnterr.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\rpcsal.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\sdkddkver.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\sdv_driverspecs.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\specstrings.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\specstrings_strict.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\specstrings_undef.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\stralign.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\tvout.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winapifamily.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\windef.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winpackagefamily.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winsmcrd.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\wnnc.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\wtypes.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\wtypesbase.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\corecrt.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\corecrt_malloc.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\corecrt_math.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\corecrt_memcpy_s.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\corecrt_memory.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\corecrt_search.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\corecrt_share.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\corecrt_stdio_config.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\corecrt_terminate.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\corecrt_wconio.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\corecrt_wctype.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\corecrt_wdirect.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\corecrt_wio.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\corecrt_wprocess.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\corecrt_wstdio.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\corecrt_wstdlib.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\corecrt_wstring.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\corecrt_wtime.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\crtdbg.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\ctype.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\errno.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\float.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\locale.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\malloc.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\math.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\share.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\stddef.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\stdio.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\stdlib.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\string.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\sys\stat.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\sys\types.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\time.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\wchar.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\apiquery2.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\cguid.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\combaseapi.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\coml2api.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\commdlg.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\consoleapi.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\consoleapi2.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\consoleapi3.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\datetimeapi.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\dde.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\ddeml.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\debugapi.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\dlgs.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\dpapi.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\enclaveapi.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\errhandlingapi.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\fibersapi.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\fileapi.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\fileapifromapp.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\handleapi.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\heapapi.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\ime_cmodes.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\imm.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\interlockedapi.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\ioapiset.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\jobapi.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\jobapi2.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\joystickapi.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\libloaderapi.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\lzexpand.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\mciapi.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\mcx.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\memoryapi.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\minwinbase.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\mmeapi.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\mmiscapi.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\mmiscapi2.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\mmsyscom.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\mmsystem.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\msxml.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\namedpipeapi.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\namespaceapi.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\nb30.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\ncrypt.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\oaidl.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\objbase.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\objidl.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\objidlbase.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\ole2.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\oleauto.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\oleidl.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\playsoundapi.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\processenv.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\processthreadsapi.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\processtopologyapi.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\profileapi.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\propidl.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\propidlbase.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\prsht.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\realtimeapiset.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\reason.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\rpcnsi.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\rpcnsip.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\securityappcontainer.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\securitybaseapi.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\servprov.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\shellapi.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\stringapiset.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\synchapi.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\sysinfoapi.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\systemtopologyapi.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\threadpoolapiset.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\threadpoollegacyapiset.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\timeapi.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\timezoneapi.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\unknwn.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\unknwnbase.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\urlmon.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\utilapiset.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\verrsrc.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winbase.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\wincon.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\wincontypes.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\wincrypt.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\Windows.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winefs.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\wingdi.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winioctl.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnetwk.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnls.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winperf.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winreg.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winscard.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winsock.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winspool.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winsvc.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winuser.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winver.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\wow64apiset.h
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\ammintrin.h
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\atomic
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\cctype
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\cerrno
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\cfloat
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\climits
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\clocale
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\cmath
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\concurrencysal.h
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\crtdefs.h
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\cstddef
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\cstdint
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\cstdio
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\cstdlib
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\cstring
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\ctime
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\cwchar
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\eh.h
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\emmintrin.h
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\exception
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\excpt.h
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\immintrin.h
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\initializer_list
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\intrin.h
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\intrin0.h
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\intrin0.inl.h
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\ios
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\iosfwd
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\iostream
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\istream
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\iterator
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\limits
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\limits.h
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\mmintrin.h
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\new
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\nmmintrin.h
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\ostream
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\pmmintrin.h
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\sal.h
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\setjmp.h
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\smmintrin.h
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\stdarg.h
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\stdexcept
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\stdint.h
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\streambuf
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\system_error
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\tmmintrin.h
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\typeinfo
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\use_ansi.h
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vadefs.h
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime.h
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new_debug.h
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_string.h
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_typeinfo.h
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\wmmintrin.h
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xatomic.h
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xcall_once.h
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xerrc.h
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xfacet
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xiosbase
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xkeycheck.h
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xlocale
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xlocinfo
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xlocnum
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmmintrin.h
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xthreads.h
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtimec.h
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtr1common
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\yvals.h
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\yvals_core.h
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\zmmintrin.h
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_iter_core.hpp
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_sanitizer_annotate_container.hpp
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_system_error_abi.hpp
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_threads_core.hpp
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_xlocinfo_types.hpp
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include\builtin_types.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include\channel_descriptor.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include\crt\common_functions.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include\crt\cudacc_ext.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include\crt\device_double_functions.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include\crt\device_double_functions.hpp
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include\crt\device_functions.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include\crt\device_functions.hpp
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include\crt\host_config.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include\crt\host_defines.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include\crt\math_functions.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include\crt\math_functions.hpp
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include\crt\sm_100_rt.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include\crt\sm_70_rt.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include\crt\sm_80_rt.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include\crt\sm_90_rt.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include\cuda_device_runtime_api.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include\cuda_runtime.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include\cuda_runtime_api.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include\device_atomic_functions.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include\device_launch_parameters.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include\device_types.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include\driver_functions.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include\driver_types.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include\library_types.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include\math_constants.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include\sm_20_atomic_functions.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include\sm_20_intrinsics.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include\sm_30_intrinsics.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include\sm_32_atomic_functions.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include\sm_32_intrinsics.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include\sm_35_atomic_functions.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include\sm_35_intrinsics.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include\sm_60_atomic_functions.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include\sm_61_intrinsics.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include\surface_indirect_functions.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include\surface_types.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include\texture_indirect_functions.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include\texture_types.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include\vector_functions.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include\vector_functions.hpp
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include\vector_types.h
F:\Catechese\EditeurAudioVideo\ImageMatter\include\UpscaleResizingKernels.cuh
