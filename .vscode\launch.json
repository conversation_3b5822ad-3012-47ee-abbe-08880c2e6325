{"version": "0.2.0", "configurations": [{"name": "(F5) Debug ImageMatter", "type": "cppvsdbg", "request": "launch", "program": "${workspaceFolder}/x64/Debug/ImageMatter.exe", "args": ["4"], "stopAtEntry": false, "cwd": "${workspaceFolder}", "environment": [{"name": "PATH", "value": "${env:PATH};${env:CUDA_PATH}/bin;${env:VccLibs}/ffmpeg/bin;${env:VccLibs}/onnxruntime/lib"}], "console": "integratedTerminal", "preLaunchTask": "Build Debug with Post-Build", "internalConsoleOptions": "neverOpen"}, {"name": "(Ctrl+F5) Release ImageMatter", "type": "cppvsdbg", "request": "launch", "program": "${workspaceFolder}/x64/Release/ImageMatter.exe", "args": ["4"], "stopAtEntry": false, "cwd": "${workspaceFolder}", "environment": [{"name": "PATH", "value": "${env:PATH};${env:CUDA_PATH}/bin;${env:VccLibs}/ffmpeg/bin;${env:VccLibs}/onnxruntime/lib"}], "console": "integratedTerminal", "preLaunchTask": "Build Release with Post-Build", "internalConsoleOptions": "neverOpen"}, {"name": "Run ImageMatter (Mode 1 - Process Alpha Video)", "type": "cppvsdbg", "request": "launch", "program": "${workspaceFolder}/x64/Debug/ImageMatter.exe", "args": ["1"], "stopAtEntry": false, "cwd": "${workspaceFolder}", "environment": [{"name": "PATH", "value": "${env:PATH};${env:CUDA_PATH}/bin;${env:VccLibs}/ffmpeg/bin;${env:VccLibs}/onnxruntime/lib"}], "console": "integratedTerminal", "preLaunchTask": "Build Debug with Post-Build", "internalConsoleOptions": "neverOpen"}, {"name": "Run ImageMatter (Mode 2 - Add AI Alpha)", "type": "cppvsdbg", "request": "launch", "program": "${workspaceFolder}/x64/Debug/ImageMatter.exe", "args": ["2"], "stopAtEntry": false, "cwd": "${workspaceFolder}", "environment": [{"name": "PATH", "value": "${env:PATH};${env:CUDA_PATH}/bin;${env:VccLibs}/ffmpeg/bin;${env:VccLibs}/onnxruntime/lib"}], "console": "integratedTerminal", "preLaunchTask": "Build Debug with Post-Build", "internalConsoleOptions": "neverOpen"}, {"name": "Run ImageMatter (Mode 3 - Synthetic Alpha)", "type": "cppvsdbg", "request": "launch", "program": "${workspaceFolder}/x64/Debug/ImageMatter.exe", "args": ["3"], "stopAtEntry": false, "cwd": "${workspaceFolder}", "environment": [{"name": "PATH", "value": "${env:PATH};${env:CUDA_PATH}/bin;${env:VccLibs}/ffmpeg/bin;${env:VccLibs}/onnxruntime/lib"}], "console": "integratedTerminal", "preLaunchTask": "Build Debug with Post-Build", "internalConsoleOptions": "neverOpen"}, {"name": "Run ImageMatter (Mode 4 - CUDA ProRes)", "type": "cppvsdbg", "request": "launch", "program": "${workspaceFolder}/x64/Debug/ImageMatter.exe", "args": ["4"], "stopAtEntry": false, "cwd": "${workspaceFolder}", "environment": [{"name": "PATH", "value": "${env:PATH};${env:CUDA_PATH}/bin;${env:VccLibs}/ffmpeg/bin;${env:VccLibs}/onnxruntime/lib"}], "console": "integratedTerminal", "preLaunchTask": "Build Debug with Post-Build", "internalConsoleOptions": "neverOpen"}]}