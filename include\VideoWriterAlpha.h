// VideoWriterAlpha.h
#pragma once

#include <string>
#include <memory>
#include <vector>
#include <map>
#include <thread>
#include <queue>
#include <mutex>
#include <condition_variable>
#include <atomic>
#include <cuda.h>
#include <cuda_runtime.h>

extern "C" {
#include <libavutil/rational.h>
}

// Forward declarations of FFmpeg structures
struct AVFormatContext;
struct AVCodecContext;
struct AVFrame;
struct AVPacket;
struct AVStream;

class VideoWriterAlpha {
public:
    // Configuration structure for output video
    struct OutputConfig {
        int width = 1920;
        int height = 1080;
        int bitRate = 50000000; // 50 Mbps default
        int gopSize = 12;
        AVRational frameRate = {30, 1}; // 30 fps default
        std::string encoder = "prores_ks"; // Default to ProRes
        std::string pixelFormat = "yuva444p10le"; // Default to 10-bit YUVA444

    private:
        std::string profile;
        bool profileSet = false;
        std::map<std::string, std::string> encoderOptions;

    public:
        // Preset configurations
        void UseProRes4444() {
            encoder = "prores_ks";
            pixelFormat = "yuva444p10le";
            profile = "4444";
            profileSet = true;
        }

        void UseProRes4444XQ() {
            encoder = "prores_ks";
            pixelFormat = "yuva444p10le";
            profile = "4444xq";
            profileSet = true;
        }

        void UseFFV1Alpha() {
            encoder = "ffv1";
            pixelFormat = "yuva420p";
            profileSet = false;
        }

        void UseUTVideoAlpha() {
            encoder = "utvideo";
            pixelFormat = "yuva420p";
        }

        // Profile management
        bool IsProfileSet() const { return profileSet; }
        const std::string& GetProfile() const { return profile; }
        void SetProfile(const std::string& p) { profile = p; profileSet = true; }

        // Encoder options management
        void SetEncoderOption(const std::string& key, const std::string& value) {
            encoderOptions[key] = value;
        }
        const std::map<std::string, std::string>& GetEncoderOptions() const {
            return encoderOptions;
        }
    };

    // Structure for frame data in encoding queue
    struct FrameData {
        void* cudaRgbaBuffer;
        size_t bufferSize;
        int64_t pts;
        bool isLastFrame;
        
        FrameData(void* buffer, size_t size, int64_t timestamp, bool last = false)
            : cudaRgbaBuffer(buffer), bufferSize(size), pts(timestamp), isLastFrame(last) {}
    };

    // Factory method to create a VideoWriterAlpha instance using a provided CUDA context
    static std::unique_ptr<VideoWriterAlpha> Create(const std::wstring& videoPath, OutputConfig& config, CUcontext cudaContext);

    // Destructor
    ~VideoWriterAlpha();

    // Write a frame from CUDA memory (RGBA format) asynchronously
    bool WriteFrame(void* cudaRgbaBuffer, size_t bufferSize);

    // Finalize the video file (waits for all pending operations to complete)
    bool Finalize();

    // Close and release resources
    void Close();

    // Get encoding queue size for monitoring
    size_t GetQueueSize() const;

    // Check if there are encoding errors
    bool HasEncodingError() const { return m_encodingError.load(); }

private:
    // Private constructor - use Create() method
    VideoWriterAlpha();

    // Initialize the writer
    bool Initialize(const std::wstring& videoPath, OutputConfig& config, CUcontext cudaContext);

    // Frame processing methods
    bool PrepareFrame(int64_t pts);
    bool TransferRgbaToFrame(void* cudaRgbaBuffer, size_t bufferSize);
    bool EncodeFrame();

    // Asynchronous encoding thread
    void EncodingThreadFunction();

    // FFmpeg context
    AVFormatContext* m_formatContext = nullptr;
    AVCodecContext* m_codecContext = nullptr;
    AVFrame* m_swFrame = nullptr; // Software frame for conversion
    AVFrame* m_hwFrame = nullptr; // Hardware frame (if supported)
    AVPacket* m_packet = nullptr;
    AVStream* m_videoStream = nullptr;

    // CUDA context and resources
    CUcontext m_cudaContext = nullptr;
    cudaStream_t m_cudaStream = nullptr;  // Stream for memory operations

    // Conversion buffers for RGBA to YUV+Alpha
    void* m_cudaYuvaBuffer = nullptr;     // YUVA buffer on GPU
    size_t m_cudaYuvaBufferSize = 0;

    // Video properties
    int m_width = 0;
    int m_height = 0;
    int m_bitRate = 0;
    AVRational* m_frameRate = nullptr;
    int m_gopSize = 0;
    int64_t m_pts = 0;
    std::string m_pixelFormat;

    // State flags
    bool m_isInitialized = false;
    bool m_isFinalized = false;
    bool m_useHardwareAcceleration = false;

    // Asynchronous encoding
    std::thread m_encodingThread;
    std::queue<FrameData> m_frameQueue;
    mutable std::mutex m_queueMutex;
    std::condition_variable m_queueCondition;
    std::condition_variable m_processingCondition;
    std::atomic<bool> m_stopEncoding{false};
    std::atomic<bool> m_encodingError{false};
    static const size_t MAX_QUEUE_SIZE = 10;
};
