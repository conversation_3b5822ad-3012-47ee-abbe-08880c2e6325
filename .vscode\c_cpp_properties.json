{"configurations": [{"name": "Win32", "includePath": ["${workspaceFolder}/include", "${env:CUDA_PATH}/include", "${env:VccLibs}/ffmpeg/include", "${env:VccLibs}/onnxruntime/include", "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/include", "C:/Program Files (x86)/Windows Kits/10/Include/10.0.26100.0/um"], "defines": ["_DEBUG", "UNICODE", "_UNICODE", "WIN32", "WIN64", "_CONSOLE"], "windowsSdkVersion": "10.0.22621.0", "compilerPath": "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/cl.exe", "cStandard": "c17", "cppStandard": "c++17", "intelliSenseMode": "windows-msvc-x64", "configurationProvider": "ms-vscode.cmake-tools"}], "version": 4}