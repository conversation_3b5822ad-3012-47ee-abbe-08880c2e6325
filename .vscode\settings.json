{"files.associations": {"*.cu": "cuda-cpp", "*.cuh": "cuda-cpp", "*.h": "c", "*.hpp": "cpp", "*.cpp": "cpp", "*.c": "c", "memory": "cpp", "iostream": "cpp", "future": "cpp", "utility": "cpp", "vector": "cpp"}, "C_Cpp.default.configurationProvider": "ms-vscode.cmake-tools", "C_Cpp.default.intelliSenseMode": "windows-msvc-x64", "C_Cpp.default.compilerPath": "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/cl.exe", "C_Cpp.default.cppStandard": "c++17", "C_Cpp.default.cStandard": "c17", "C_Cpp.errorSquiggles": "enabled", "C_Cpp.intelliSenseEngine": "default", "C_Cpp.autocomplete": "default", "C_Cpp.formatting": "vcFormat", "editor.formatOnSave": false, "editor.insertSpaces": true, "editor.tabSize": 4, "editor.detectIndentation": true, "files.trimTrailingWhitespace": true, "files.insertFinalNewline": true, "files.trimFinalNewlines": true, "terminal.integrated.defaultProfile.windows": "Command Prompt", "terminal.integrated.profiles.windows": {"Command Prompt": {"path": "C:\\Windows\\System32\\cmd.exe", "args": [], "icon": "terminal-cmd"}, "PowerShell": {"source": "PowerShell", "icon": "terminal-powershell"}, "Developer Command Prompt": {"path": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\Tools\\VsDevCmd.bat", "args": ["-arch=x64", "-host_arch=x64"], "icon": "terminal-cmd"}}, "cmake.configureOnOpen": false, "search.exclude": {"**/x64": true, "**/.vs": true, "**/Debug": true, "**/Release": true, "**/*.vcxproj.user": true}, "files.exclude": {"**/.vs": true, "**/x64/Debug": true, "**/x64/Release": true, "**/*.vcxproj.user": true}, "codium.codeCompletion.enable": false}