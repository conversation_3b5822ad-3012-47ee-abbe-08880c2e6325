#include "Helpers_Kernels.cuh"
#include <iostream>

#ifdef __CUDACC__

// CUDA kernel to draw a rectangle on RGB image
__global__ void drawRectangleKernel(float* image, int width, int height,
    int x1, int y1, int x2, int y2,
    float r, float g, float b, int thickness) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    int idy = blockIdx.y * blockDim.y + threadIdx.y;

    if (idx >= width || idy >= height) return;

    // Check if pixel is on the rectangle border
    bool onBorder = false;

    // Top and bottom edges
    if ((idy >= y1 && idy < y1 + thickness && idx >= x1 && idx <= x2) ||
        (idy <= y2 && idy > y2 - thickness && idx >= x1 && idx <= x2)) {
        onBorder = true;
    }

    // Left and right edges
    if ((idx >= x1 && idx < x1 + thickness && idy >= y1 && idy <= y2) ||
        (idx <= x2 && idx > x2 - thickness && idy >= y1 && idy <= y2)) {
        onBorder = true;
    }

    if (onBorder) {
        int pixelIdx = (idy * width + idx) * 3;  // Changed from * 4 to * 3
        image[pixelIdx + 0] = r;
        image[pixelIdx + 1] = g;
        image[pixelIdx + 2] = b;
        // Removed alpha channel assignment
    }
}

// CUDA kernel to draw text (simple bitmap font for numbers) on RGB image
__global__ void drawNumberKernel(float* image, int width, int height,
    int x, int y, int number,
    float r, float g, float b) {  // Removed alpha parameter
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    int idy = blockIdx.y * blockDim.y + threadIdx.y;

    if (idx >= width || idy >= height) return;

    // Simple 5x7 bitmap font for digits 0-9
    const bool digits[10][35] = {
        // 0
        {1,1,1,1,1, 1,0,0,0,1, 1,0,0,0,1, 1,0,0,0,1, 1,0,0,0,1, 1,0,0,0,1, 1,1,1,1,1},
        // 1
        {0,0,1,0,0, 0,1,1,0,0, 0,0,1,0,0, 0,0,1,0,0, 0,0,1,0,0, 0,0,1,0,0, 0,1,1,1,0},
        // 2
        {1,1,1,1,1, 0,0,0,0,1, 0,0,0,0,1, 1,1,1,1,1, 1,0,0,0,0, 1,0,0,0,0, 1,1,1,1,1},
        // 3
        {1,1,1,1,1, 0,0,0,0,1, 0,0,0,0,1, 1,1,1,1,1, 0,0,0,0,1, 0,0,0,0,1, 1,1,1,1,1},
        // 4
        {1,0,0,0,1, 1,0,0,0,1, 1,0,0,0,1, 1,1,1,1,1, 0,0,0,0,1, 0,0,0,0,1, 0,0,0,0,1},
        // 5
        {1,1,1,1,1, 1,0,0,0,0, 1,0,0,0,0, 1,1,1,1,1, 0,0,0,0,1, 0,0,0,0,1, 1,1,1,1,1},
        // 6
        {1,1,1,1,1, 1,0,0,0,0, 1,0,0,0,0, 1,1,1,1,1, 1,0,0,0,1, 1,0,0,0,1, 1,1,1,1,1},
        // 7
        {1,1,1,1,1, 0,0,0,0,1, 0,0,0,0,1, 0,0,0,0,1, 0,0,0,0,1, 0,0,0,0,1, 0,0,0,0,1},
        // 8
        {1,1,1,1,1, 1,0,0,0,1, 1,0,0,0,1, 1,1,1,1,1, 1,0,0,0,1, 1,0,0,0,1, 1,1,1,1,1},
        // 9
        {1,1,1,1,1, 1,0,0,0,1, 1,0,0,0,1, 1,1,1,1,1, 0,0,0,0,1, 0,0,0,0,1, 1,1,1,1,1}
    };

    int localX = idx - x;
    int localY = idy - y;

    if (localX >= 0 && localX < 5 && localY >= 0 && localY < 7 && number >= 0 && number <= 9) {
        if (digits[number][localY * 5 + localX]) {
            int pixelIdx = (idy * width + idx) * 3;  // Changed from * 4 to * 3
            image[pixelIdx + 0] = r;
            image[pixelIdx + 1] = g;
            image[pixelIdx + 2] = b;
            // Removed alpha channel assignment
        }
    }
}

// Kernel launcher functions
bool LaunchDrawRectangleKernel(float* image, int width, int height,
    int x1, int y1, int x2, int y2,
    float r, float g, float b,
    int thickness,
    cudaStream_t stream) {
    // Define CUDA execution configuration
    dim3 blockSize(16, 16);
    dim3 gridSize((width + blockSize.x - 1) / blockSize.x,
        (height + blockSize.y - 1) / blockSize.y);

    // Launch kernel
    drawRectangleKernel<<<gridSize, blockSize, 0, stream>>>(
        image, width, height, x1, y1, x2, y2, r, g, b, thickness
    );

    // Check for kernel launch errors
    cudaError_t error = cudaGetLastError();
    if (error != cudaSuccess) {
        std::cerr << "CUDA kernel launch error in LaunchDrawRectangleKernel: "
            << cudaGetErrorString(error) << std::endl;
        return false;
    }

    return true;
}

bool LaunchDrawNumberKernel(float* image, int width, int height,
    int x, int y, int number,
    float r, float g, float b,
    cudaStream_t stream) {
    // Define CUDA execution configuration
    dim3 blockSize(16, 16);
    dim3 gridSize((width + blockSize.x - 1) / blockSize.x,
        (height + blockSize.y - 1) / blockSize.y);

    // Launch kernel
    drawNumberKernel<<<gridSize, blockSize, 0, stream>>>(
        image, width, height, x, y, number, r, g, b
    );

    // Check for kernel launch errors
    cudaError_t error = cudaGetLastError();
    if (error != cudaSuccess) {
        std::cerr << "CUDA kernel launch error in LaunchDrawNumberKernel: "
            << cudaGetErrorString(error) << std::endl;
        return false;
    }

    return true;
}

#endif