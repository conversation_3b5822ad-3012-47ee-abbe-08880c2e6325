#ifndef ALPHA_CODEC_H
#define ALPHA_CODEC_H

#include <cuda_runtime.h>
#include <cstdint>
#include <cstdlib>

constexpr int REPEAT_SHORT = 1;
constexpr int REPEAT_LONG = 254;

// Structure to hold compressed alpha data
struct CompressedAlphaData {
	uint8_t* compressed_data;
	size_t compressed_size;
	uint32_t width;
	uint32_t height;
	uint32_t alpha_length;

	CompressedAlphaData() : compressed_data(nullptr), compressed_size(0),
							width(0), height(0), alpha_length(0) {}

	~CompressedAlphaData() {
		if (compressed_data) {
			free(compressed_data);
			compressed_data = nullptr;
		}
	}
};

// Function declarations
extern "C" {
/**
 * Compress alpha channel data using CUDA
 * @param d_alpha_input CUDA device buffer containing alpha values (0-255)
 * @param width Image width
 * @param height Image height
 * @param stream CUDA stream for async execution
 * @return CompressedAlphaData structure containing compressed data on CPU
 */
CompressedAlphaData* compress_alpha_cuda(const uint8_t* d_alpha_input,
	uint32_t width,
	uint32_t height,
	cudaStream_t stream = 0);

/**
 * Decompress alpha channel data using CUDA
 * @param compressed_data Compressed alpha data structure
 * @param d_alpha_output CUDA device buffer to store decompressed alpha (must be pre-allocated)
 * @param stream CUDA stream for async execution
 * @return cudaError_t indicating success or failure
 */
cudaError_t decompress_alpha_cuda(const CompressedAlphaData* compressed_data,
	uint8_t* d_alpha_output,
	cudaStream_t stream = 0);

/**
 * Free compressed alpha data
 * @param data Pointer to CompressedAlphaData to free
 */
void free_compressed_alpha_data(CompressedAlphaData* data);
}

#endif // ALPHA_CODEC_H
