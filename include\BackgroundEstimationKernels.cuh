#pragma once

#include <cuda_runtime.h>

// Background estimation CUDA kernel launcher functions
extern "C" {
    /**
     * Estimate background horizontally by scanning each line from left to right
     * Finds complete background pixels and interpolates between them
     * @param alphaData Input alpha matte data (width * height floats)
     * @param rgbData Input RGB data in planar format (3 * width * height floats)
     * @param horizontalBgData Output horizontal background estimates (opaque structure)
     * @param width Image width
     * @param height Image height
     * @param stream CUDA stream for asynchronous execution
     */
    void launchEstimateBackgroundHorizontal(
        const float* alphaData,
        const float* rgbData,
        void* horizontalBgData,
        int width,
        int height,
        cudaStream_t stream
    );

    /**
     * Estimate background vertically by scanning each column from top to bottom
     * Finds complete background pixels and interpolates between them
     * @param alphaData Input alpha matte data (width * height floats)
     * @param rgbData Input RGB data in planar format (3 * width * height floats)
     * @param verticalBgData Output vertical background estimates (opaque structure)
     * @param width Image width
     * @param height Image height
     * @param stream CUDA stream for asynchronous execution
     */
    void launchEstimateBackgroundVertical(
        const float* alphaData,
        const float* rgbData,
        void* verticalBgData,
        int width,
        int height,
        cudaStream_t stream
    );

    /**
     * Extract foreground using estimated background and alpha compositing formula
     * For semi-transparent pixels: foreground = (output - (1-alpha) × background) / alpha
     * For pure background/foreground pixels: uses original RGB values
     * @param alphaData Input alpha matte data (width * height floats)
     * @param originalRgbData Original RGB data in planar format (3 * width * height floats)
     * @param horizontalBgData Horizontal background estimates from first kernel
     * @param verticalBgData Vertical background estimates from second kernel
     * @param finalRgbaData Output RGBA data with extracted foreground (4 * width * height floats)
     * @param width Image width
     * @param height Image height
     * @param stream CUDA stream for asynchronous execution
     */
    void launchExtractForeground(
        const float* alphaData,
        const float* originalRgbData,
        const void* horizontalBgData,
        const void* verticalBgData,
        float* finalRgbaData,
        int width,
        int height,
        cudaStream_t stream
    );
}

// Helper function to calculate the size needed for background data buffers
inline size_t getBackgroundDataBufferSize(int width, int height) {
    // Each pixel needs 4 floats: R, G, B, reliability
    return width * height * 4 * sizeof(float);
}
