#include "ImageMattingTensorRt.h"
#include "Helpers.h"
#include "Matting_Kernels.cuh"  // For LaunchPreprocessBufferKernel and LanczosResizeKernelLauncher
#include "DownscaleResizingKernels.cuh"
#include "UpscaleResizingKernels.cuh"
#include <iostream>
#include <fstream>
#include <cuda_runtime.h>

ImageMattingTensorRt::ImageMattingTensorRt()
    : m_logger(nullptr)
    , m_runtime(nullptr)
    , m_engine(nullptr)
    , m_context(nullptr)
    , m_inputIndex(-1)
    , m_outputIndex(-1)
    , m_modelInputWidth(0)
    , m_modelInputHeight(0)
    , m_imageWidth(0)
    , m_imageHeight(0)
    , m_scaleRatioX(1.0f)
    , m_scaleRatioY(1.0f)
    , m_deviceInputBuffer(nullptr)
    , m_deviceOutputBuffer(nullptr)
    , m_deviceResizedInputBuffer(nullptr)
    , m_deviceResizedOutputBuffer(nullptr)
    , m_preprocessedBuffer(nullptr)
    , m_inputBufferSize(0)
    , m_outputBufferSize(0)
    , m_preprocessedBufferSize(0)
    , m_cudaStream(nullptr)
    , m_isRgba(false)
    , m_initialized(false)
    , m_normalizationParams(NormalizationParams::ImageNet())
    , m_resizeMethod(ResizeMethod::EXTEND_SHRINK_LANCZOS)
{
}

ImageMattingTensorRt::~ImageMattingTensorRt() {
    Shutdown();
}

bool ImageMattingTensorRt::Init(
    const wchar_t* enginePath,
    int modelWidth,
    int modelHeight,
    int imageWidth,
    int imageHeight,
    const NormalizationParams& normParams,
    bool isRgba,
    ResizeMethod resizeMethod,
    cudaStream_t externalStream) {

    try {
        m_modelInputWidth = modelWidth;
        m_modelInputHeight = modelHeight;
        m_imageWidth = imageWidth;
        m_imageHeight = imageHeight;
        m_normalizationParams = normParams;
        m_isRgba = isRgba;
        m_resizeMethod = resizeMethod;
        m_cudaStream = externalStream;

        // Create TensorRT logger
        m_logger = std::make_unique<TensorRtLogger>();

        // Load TensorRT engine
        if (!LoadEngine(enginePath)) {
            std::cerr << "Failed to load TensorRT engine" << std::endl;
            return false;
        }

        // Calculate buffer sizes
        int inputChannels = m_isRgba ? 4 : 3;
        m_inputBufferSize = m_imageWidth * m_imageHeight * inputChannels * sizeof(float);
        m_outputBufferSize = m_imageWidth * m_imageHeight * sizeof(float);
        m_preprocessedBufferSize = m_imageWidth * m_imageHeight * inputChannels * sizeof(float);

        // Allocate CUDA buffers
        CUDA_CHECK(cudaMalloc(&m_deviceInputBuffer, m_inputBufferSize));
        CUDA_CHECK(cudaMalloc(&m_deviceOutputBuffer, m_outputBufferSize));

        // Check if resizing is needed
        bool needsResize = (m_imageWidth != m_modelInputWidth || m_imageHeight != m_modelInputHeight);

        if (needsResize) {
            // Allocate buffers only when resizing is needed
            CUDA_CHECK(cudaMalloc(&m_preprocessedBuffer, m_preprocessedBufferSize));

            // Allocate model input/output buffers
            size_t modelInputSize = m_modelInputWidth * m_modelInputHeight * inputChannels * sizeof(float);
            size_t modelOutputSize = m_modelInputWidth * m_modelInputHeight * sizeof(float);

            CUDA_CHECK(cudaMalloc(&m_deviceResizedInputBuffer, modelInputSize));
            CUDA_CHECK(cudaMalloc(&m_deviceResizedOutputBuffer, modelOutputSize));
        } else {
            // When no resizing is needed, use the original buffers directly as model buffers
            m_preprocessedBuffer = nullptr;  // Not needed
            m_deviceResizedInputBuffer = m_deviceInputBuffer;   // Point to original input buffer
            m_deviceResizedOutputBuffer = m_deviceOutputBuffer; // Point to original output buffer
        }

        // Setup bindings after buffers are allocated
        if (!SetupBindings()) {
            std::cerr << "Failed to setup TensorRT bindings" << std::endl;
            return false;
        }

        // Calculate scale ratios
        m_scaleRatioX = static_cast<float>(m_modelInputWidth) / m_imageWidth;
        m_scaleRatioY = static_cast<float>(m_modelInputHeight) / m_imageHeight;

        m_initialized = true;
        std::cout << "ImageMattingTensorRt initialization successful." << std::endl;
        std::cout << "Input buffer size: " << m_inputBufferSize << " bytes" << std::endl;
        std::cout << "Output buffer size: " << m_outputBufferSize << " bytes" << std::endl;
        std::cout << "Model dimensions: " << m_modelInputWidth << "x" << m_modelInputHeight << std::endl;
        std::cout << "Image dimensions: " << m_imageWidth << "x" << m_imageHeight << std::endl;

        return true;
    }
    catch (const std::exception& e) {
        std::cerr << "Exception in ImageMattingTensorRt::Init: " << e.what() << std::endl;
        CleanupResources();
        return false;
    }
}

bool ImageMattingTensorRt::Infer() {
    if (!m_initialized || !m_context) {
        std::cerr << "ImageMattingTensorRt not initialized" << std::endl;
        return false;
    }

    try {
        // Preprocess input using common base class implementation
        cudaError_t preprocessResult = PreprocessInputBufferCommon(
            m_deviceInputBuffer,
            m_preprocessedBuffer,  // Will be nullptr when no resizing is needed
            m_deviceResizedInputBuffer,
            m_imageWidth,
            m_imageHeight,
            m_modelInputWidth,
            m_modelInputHeight,
            m_isRgba,
            m_normalizationParams,
            m_preprocessedBufferSize,
            m_resizeMethod,
            m_cudaStream
        );
        if (preprocessResult != cudaSuccess) {
            std::cerr << "Preprocessing failed: " << cudaGetErrorString(preprocessResult) << std::endl;
            return false;
        }

         //SavePlanarFloatImageToPNG("Videos\\temp.png", m_deviceResizedInputBuffer, m_modelInputWidth, m_modelInputHeight, m_isRgba, m_cudaStream);

        // Run TensorRT inference (TensorRT 10.x API)
        bool inferenceResult = m_context->enqueueV3(m_cudaStream);
        if (!inferenceResult) {
            std::cerr << "TensorRT inference failed" << std::endl;
            return false;
        }

        //SaveAlphaMatteToPNG("Videos\\alpha_trt_head.png", m_deviceResizedOutputBuffer, m_modelInputWidth, m_modelInputHeight);


        // Postprocess output using common base class implementation
        cudaError_t postprocessResult = PostprocessOutputBufferCommon(
            m_deviceResizedOutputBuffer, m_deviceOutputBuffer,
            m_modelInputWidth, m_modelInputHeight,
            m_imageWidth, m_imageHeight,
            m_resizeMethod,
            m_cudaStream);

        if (postprocessResult != cudaSuccess) {
            std::cerr << "Output postprocessing failed: " << cudaGetErrorString(postprocessResult) << std::endl;
            return false;
        }

        return true;
    }
    catch (const std::exception& e) {
        std::cerr << "Exception in ImageMattingTensorRt::Infer: " << e.what() << std::endl;
        return false;
    }
}

void ImageMattingTensorRt::Shutdown() {
    CleanupResources();
    m_initialized = false;
}

bool ImageMattingTensorRt::LoadEngine(const wchar_t* enginePath) {
    try {
        // Convert wide string to string
        std::string enginePathStr = ConvertWCharToChar(enginePath);

        // Read engine file
        std::ifstream file(enginePathStr, std::ios::binary);
        if (!file.good()) {
            std::cerr << "Failed to open engine file: " << enginePathStr << std::endl;
            return false;
        }

        file.seekg(0, std::ios::end);
        size_t size = file.tellg();
        file.seekg(0, std::ios::beg);

        std::vector<char> engineData(size);
        file.read(engineData.data(), size);
        if (!file) {
            std::cerr << "Failed to read the full engine file: " << enginePathStr << std::endl;
            return false;
        }
        file.close();

        // Create TensorRT runtime
        m_runtime.reset(nvinfer1::createInferRuntime(*m_logger));
        if (!m_runtime) {
            std::cerr << "Failed to create TensorRT runtime" << std::endl;
            return false;
        }

        // Deserialize engine
        m_engine.reset(m_runtime->deserializeCudaEngine(engineData.data(), size));
        if (!m_engine) {
            std::cerr << "Failed to deserialize TensorRT engine" << std::endl;
            return false;
        }

        // Create execution context
        m_context.reset(m_engine->createExecutionContext());
        if (!m_context) {
            std::cerr << "Failed to create TensorRT execution context" << std::endl;
            return false;
        }

        std::cout << "TensorRT engine loaded successfully from: " << enginePathStr << std::endl;
        return true;
    }
    catch (const std::exception& e) {
        std::cerr << "Exception in LoadEngine: " << e.what() << std::endl;
        return false;
    }
}

bool ImageMattingTensorRt::SetupBindings() {
    try {
        // TensorRT 10.x API
        int numIOTensors = m_engine->getNbIOTensors();

        for (int i = 0; i < numIOTensors; ++i) {
            const char* name = m_engine->getIOTensorName(i);
            nvinfer1::TensorIOMode ioMode = m_engine->getTensorIOMode(name);
            auto dims = m_engine->getTensorShape(name);

            if (ioMode == nvinfer1::TensorIOMode::kINPUT) {
                m_inputIndex = i;
                m_inputName = name;
                // Model dimensions are now provided explicitly, not extracted from engine
                std::cout << "Input tensor: " << name << " [" << dims.d[0] << "x" << dims.d[1]
                         << "x" << dims.d[2] << "x" << dims.d[3] << "]" << std::endl;
                std::cout << "Using provided model dimensions: " << m_modelInputWidth << "x" << m_modelInputHeight << std::endl;

                // Set input tensor address
                m_context->setTensorAddress(name, m_deviceResizedInputBuffer);
            } else if (ioMode == nvinfer1::TensorIOMode::kOUTPUT) {
                m_outputIndex = i;
                m_outputName = name;
                std::cout << "Output tensor: " << name << " [" << dims.d[0] << "x" << dims.d[1]
                         << "x" << dims.d[2] << "x" << dims.d[3] << "]" << std::endl;

                // Set output tensor address
                m_context->setTensorAddress(name, m_deviceResizedOutputBuffer);
            }
        }

        return (m_inputIndex >= 0 && m_outputIndex >= 0);
    }
    catch (const std::exception& e) {
        std::cerr << "Exception in SetupBindings: " << e.what() << std::endl;
        return false;
    }
}

void ImageMattingTensorRt::CleanupResources() {
	// Free CUDA buffers first
	if (m_deviceInputBuffer) {
		cudaFree(m_deviceInputBuffer);
		m_deviceInputBuffer = nullptr;
	}
	if (m_deviceOutputBuffer) {
		cudaFree(m_deviceOutputBuffer);
		m_deviceOutputBuffer = nullptr;
	}

	// Handle resized buffers
	if (m_deviceResizedInputBuffer && m_deviceResizedInputBuffer != m_deviceInputBuffer) {
		cudaFree(m_deviceResizedInputBuffer);
	}
	m_deviceResizedInputBuffer = nullptr;

	if (m_deviceResizedOutputBuffer && m_deviceResizedOutputBuffer != m_deviceOutputBuffer) {
		cudaFree(m_deviceResizedOutputBuffer);
	}
	m_deviceResizedOutputBuffer = nullptr;

	if (m_preprocessedBuffer) {
		cudaFree(m_preprocessedBuffer);
		m_preprocessedBuffer = nullptr;
	}

	// CRITICAL: TensorRT objects must be destroyed in reverse order of creation
	m_context.reset(); // Destroy context first
	m_engine.reset();  // Then engine
	m_runtime.reset(); // Finally runtime
	m_logger.reset();  // Logger can be last
}
