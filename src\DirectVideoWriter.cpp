// DirectVideoWriter.cpp
#include "DirectVideoWriter.h"
#include <stdexcept>
#include <iostream>
#include <cmath>

// FFmpeg includes
extern "C" {
#include <libavcodec/avcodec.h>
#include <libavformat/avformat.h>
#include <libavutil/hwcontext.h>
#include <libavutil/hwcontext_cuda.h>
#include <libavutil/opt.h>
#include <libavutil/rational.h>
#include <libavutil/error.h>
#include <libavutil/mem.h>
#include <libavutil/imgutils.h>
#include <libavutil/mathematics.h>
}

// Helper to check for CUDA errors (from previous version)
static void CheckCudaError(cudaError_t error)
{
    if (error != cudaSuccess) {
        throw std::runtime_error(std::string("CUDA error: ") + cudaGetErrorString(error));
    }
}

// Helper to convert wstring to string (from previous version)
static std::string WStringToString(const std::wstring& wstr)
{
    if (wstr.empty()) return std::string();
    int size_needed = WideCharToMultiByte(CP_UTF8, 0, &wstr[0], (int)wstr.size(), NULL, 0, NULL, NULL);
    std::string strTo(size_needed, 0);
    WideCharToMultiByte(CP_UTF8, 0, &wstr[0], (int)wstr.size(), &strTo[0], size_needed, NULL, NULL);
    return strTo;
}

// Helper to create AVRational (from previous version)
static AVRational* CreateAVRational(int num, int den)
{
    AVRational* r = new AVRational;
    r->num = num;
    r->den = den;
    return r;
}

// Helper to delete AVRational (from previous version)
static void DeleteAVRational(AVRational* r)
{
    delete r;
}

// Helper to normalize AVRational to reasonable values for encoding
static AVRational NormalizeFrameRate(const AVRational& frameRate) {
    // Convert to double and back to get a reasonable fraction
    double fps = static_cast<double>(frameRate.num) / frameRate.den;

    // For common frame rates, use exact values
    if (abs(fps - 23.976) < 0.001) return {24000, 1001};
    if (abs(fps - 24.0) < 0.001) return {24, 1};
    if (abs(fps - 25.0) < 0.001) return {25, 1};
    if (abs(fps - 29.97) < 0.001) return {30000, 1001};
    if (abs(fps - 30.0) < 0.001) return {30, 1};
    if (abs(fps - 50.0) < 0.001) return {50, 1};
    if (abs(fps - 59.94) < 0.001) return {60000, 1001};
    if (abs(fps - 60.0) < 0.001) return {60, 1};

    // For other frame rates, use a simpler approach to avoid large fractions
    // Round to nearest 0.001 and use a reasonable denominator
    int roundedFps = static_cast<int>(fps * 1000 + 0.5);

    // Use a smaller denominator to avoid huge time_base values
    // Try common denominators first
    int denominators[] = {1, 2, 4, 5, 8, 10, 25, 50, 100, 1000};

    for (int den : denominators) {
        int num = static_cast<int>(fps * den + 0.5);
        double testFps = static_cast<double>(num) / den;
        if (abs(testFps - fps) < 0.01) { // Within 0.01 fps tolerance
            return {num, den};
        }
    }

    // Fallback: use a reasonable fraction with limited precision
    return {static_cast<int>(fps * 100 + 0.5), 100};
}


// Constructor
DirectVideoWriter::DirectVideoWriter()
    : m_formatContext(nullptr)
    , m_codecContext(nullptr)
    , m_videoStream(nullptr)
    , m_swFrame(nullptr)
    , m_hwFrame(nullptr)
    , m_packet(nullptr)
    , m_cudaContext(nullptr)
    , m_cudaStream(0)
    , m_encoderStream(0)
    , m_width(0)
    , m_height(0)
    , m_bitRate(0)
    , m_frameRate(nullptr)
    , m_gopSize(0)
    , m_pts(0)
    , m_isInitialized(false)
    , m_isFinalized(false)
{
}

// Destructor
DirectVideoWriter::~DirectVideoWriter()
{
    Close();
}

// Create a new video writer
std::unique_ptr<DirectVideoWriter> DirectVideoWriter::Create(const std::wstring& videoPath, OutputConfig& config, CUcontext cudaContext)
{
    std::unique_ptr<DirectVideoWriter> writer(new DirectVideoWriter());
    if (writer->Initialize(videoPath, config, cudaContext)) {
        return writer;
    }
    return nullptr;
}

// Initialize the video writer
bool DirectVideoWriter::Initialize(const std::wstring& videoPath, OutputConfig& config, CUcontext cudaContext)
{
    // Enable debug logging to get detailed error info from FFmpeg
    //av_log_set_level(AV_LOG_DEBUG);

    AVBufferRef* hw_device_ctx = nullptr;
    AVBufferRef* hw_frames_ref = nullptr;

    try {
        m_cudaContext = cudaContext;
        if (!m_cudaContext) {
            throw std::runtime_error("A valid CUDA context must be provided.");
        }

        CheckCudaError(cudaStreamCreate(&m_cudaStream));
        CheckCudaError(cudaStreamCreate(&m_encoderStream));

        std::string filePath = WStringToString(videoPath);
        if (avformat_alloc_output_context2(&m_formatContext, nullptr, nullptr, filePath.c_str()) < 0) {
            throw std::runtime_error("Could not allocate output format context.");
        }

        const AVCodec* encoder = avcodec_find_encoder_by_name(config.encoder.c_str());
        if (!encoder) {
            throw std::runtime_error("Could not find encoder: " + config.encoder);
        }

        m_videoStream = avformat_new_stream(m_formatContext, encoder);
        if (!m_videoStream) {
            throw std::runtime_error("Could not create new video stream.");
        }

        m_codecContext = avcodec_alloc_context3(encoder);
        if (!m_codecContext) {
            throw std::runtime_error("Could not allocate codec context.");
        }

        // --- HARDWARE CONTEXT SETUP ---
        // 1. Create the HW Device Context referencing the user's CUcontext
        hw_device_ctx = av_hwdevice_ctx_alloc(AV_HWDEVICE_TYPE_CUDA);
        if (!hw_device_ctx) throw std::runtime_error("Failed to create CUDA hardware device context.");

        auto* device_ctx = (AVHWDeviceContext*)hw_device_ctx->data;
        auto* cuda_hw_ctx = (AVCUDADeviceContext*)device_ctx->hwctx;
        cuda_hw_ctx->cuda_ctx = m_cudaContext;
        cuda_hw_ctx->stream = m_encoderStream;
        if (av_hwdevice_ctx_init(hw_device_ctx) < 0) {
            throw std::runtime_error("Failed to initialize CUDA hardware device context.");
        }

        // 2. Create the HW Frames Context
        hw_frames_ref = av_hwframe_ctx_alloc(hw_device_ctx);
        if (!hw_frames_ref) throw std::runtime_error("Failed to create CUDA hardware frames context.");

        auto* frames_ctx = (AVHWFramesContext*)hw_frames_ref->data;
        frames_ctx->format = AV_PIX_FMT_CUDA;
        frames_ctx->sw_format = AV_PIX_FMT_NV12; // Corresponding software format
        frames_ctx->width = config.width;
        frames_ctx->height = config.height;
        frames_ctx->initial_pool_size = 20; // A decent pool size

        if (av_hwframe_ctx_init(hw_frames_ref) < 0) {
            throw std::runtime_error("Failed to initialize CUDA hardware frames context.");
        }

        // 3. Set codec parameters and assign the contexts
        m_codecContext->hw_device_ctx = av_buffer_ref(hw_device_ctx);
        m_codecContext->hw_frames_ctx = av_buffer_ref(hw_frames_ref);
        if (!m_codecContext->hw_device_ctx || !m_codecContext->hw_frames_ctx) {
            throw std::runtime_error("Failed to set hardware contexts on codec.");
        }

        m_width = config.width;
        m_height = config.height;

        // Normalize the frame rate to avoid encoding issues with large numerators/denominators
        AVRational normalizedFrameRate = NormalizeFrameRate(config.frameRate);
        m_frameRate = CreateAVRational(normalizedFrameRate.num, normalizedFrameRate.den);
        m_gopSize = config.gopSize;
        m_quality = config.IsQualitySet() ? config.GetQuality() : 20;

        std::cout << "DirectVideoWriter: Original frame rate: " << config.frameRate.num << "/" << config.frameRate.den
                  << " (" << (static_cast<double>(config.frameRate.num) / config.frameRate.den) << " fps)" << std::endl;
        std::cout << "DirectVideoWriter: Normalized frame rate: " << normalizedFrameRate.num << "/" << normalizedFrameRate.den
                  << " (" << (static_cast<double>(normalizedFrameRate.num) / normalizedFrameRate.den) << " fps)" << std::endl;

        // Also show the time_base that will be used
        //AVRational timeBase = av_inv_q(normalizedFrameRate);
        //std::cout << "DirectVideoWriter: Time base: " << timeBase.num << "/" << timeBase.den
        //          << " (" << (static_cast<double>(timeBase.num) / timeBase.den) << " seconds per frame)" << std::endl;

        m_codecContext->width = m_width;
        m_codecContext->height = m_height;

        // Use a standard time_base that works well with most containers
        // Common practice is to use 1/90000 for H.264 or a simple fraction
        AVRational timeBase = {1, 90000};  // 90kHz time base (common for H.264)
        m_codecContext->time_base = timeBase;
        m_codecContext->framerate = *m_frameRate;
        m_codecContext->gop_size = m_gopSize;
        m_codecContext->max_b_frames = 0;
        m_codecContext->pix_fmt = AV_PIX_FMT_CUDA; // Specify we are providing CUDA frames

        std::cout << "DirectVideoWriter: Using time_base: " << timeBase.num << "/" << timeBase.den << std::endl;

        AVDictionary* options = nullptr;
        std::string preset = config.IsPresetSet() ? config.GetPreset() : "p6";
        av_dict_set(&options, "preset", preset.c_str(), 0);

        if (m_quality > 0) {
            m_codecContext->bit_rate = 0; // Bitrate is ignored in Constant Quality mode
            av_dict_set_int(&options, "cq", m_quality, 0);
        }
        else {
            m_codecContext->bit_rate = config.bitRate;
        }

        // 4. Open the codec now that all contexts are set up
        if (avcodec_open2(m_codecContext, encoder, &options) < 0) {
            av_dict_free(&options);
            throw std::runtime_error("Could not open codec. Check logs for details.");
        }
        av_dict_free(&options);

        if (avcodec_parameters_from_context(m_videoStream->codecpar, m_codecContext) < 0) {
            throw std::runtime_error("Could not copy codec parameters to stream.");
        }

        // Set the stream's time_base to match the codec's time_base
        m_videoStream->time_base = m_codecContext->time_base;
        std::cout << "DirectVideoWriter: Stream time_base set to: " << m_videoStream->time_base.num << "/" << m_videoStream->time_base.den << std::endl;

        // Calculate and display expected PTS increment
        double fps = static_cast<double>(m_frameRate->num) / m_frameRate->den;
        int64_t ptsIncrement = static_cast<int64_t>(90000.0 / fps + 0.5);
        std::cout << "DirectVideoWriter: PTS increment per frame: " << ptsIncrement << " (for " << fps << " fps)" << std::endl;

        m_hwFrame = av_frame_alloc();
        m_packet = av_packet_alloc();
        if (!m_hwFrame || !m_packet) throw std::runtime_error("Could not allocate frame or packet.");

        // Allocate the HW frame buffer from our initialized context pool
        if (av_hwframe_get_buffer(m_codecContext->hw_frames_ctx, m_hwFrame, 0) < 0) {
            throw std::runtime_error("Could not allocate hardware frame buffer.");
        }

        if (!(m_formatContext->oformat->flags & AVFMT_NOFILE)) {
            if (avio_open(&m_formatContext->pb, filePath.c_str(), AVIO_FLAG_WRITE) < 0) {
                throw std::runtime_error("Could not open output file: " + filePath);
            }
        }

        if (avformat_write_header(m_formatContext, nullptr) < 0) {
            throw std::runtime_error("Could not write video header.");
        }

        av_buffer_unref(&hw_frames_ref);
        av_buffer_unref(&hw_device_ctx);
        m_isInitialized = true;
        return true;
    }
    catch (const std::exception& e) {
        std::cerr << "Error initializing DirectVideoWriter: " << e.what() << std::endl;
        av_buffer_unref(&hw_frames_ref);
        av_buffer_unref(&hw_device_ctx);
        Close();
        return false;
    }
}

bool DirectVideoWriter::PrepareFrame() {
    // Calculate PTS increment based on time_base and frame rate
    // With 90kHz time_base, PTS increment = 90000 / fps
    double fps = static_cast<double>(m_frameRate->num) / m_frameRate->den;
    int64_t ptsIncrement = static_cast<int64_t>(90000.0 / fps + 0.5);

    m_hwFrame->pts = m_pts;
    m_pts += ptsIncrement;

    return true;
}

bool DirectVideoWriter::TransferCudaToFrame(void* cudaBuffer, size_t bufferSize, size_t bufferPitch) {
    size_t expectedSize = m_width * m_height * 3 / 2;
    if (bufferSize < expectedSize) {
        std::cerr << "Buffer size too small. Expected at least " << expectedSize << ", Got: " << bufferSize << std::endl;
        return false;
    }

    // Copy Y plane (luma) using the actual buffer pitch
    CheckCudaError(cudaMemcpy2DAsync(
        m_hwFrame->data[0], m_hwFrame->linesize[0],
        cudaBuffer, bufferPitch,
        m_width, m_height,
        cudaMemcpyDeviceToDevice, m_cudaStream
    ));

    // Copy UV plane (chroma) - NV12 format, using the actual buffer pitch
    size_t yPlaneSize = m_height * bufferPitch;
    void* uvSource = static_cast<char*>(cudaBuffer) + yPlaneSize;
    CheckCudaError(cudaMemcpy2DAsync(
        m_hwFrame->data[1], m_hwFrame->linesize[1],
        uvSource, bufferPitch,
        m_width, m_height / 2,
        cudaMemcpyDeviceToDevice, m_cudaStream
    ));
    return true;
}

bool DirectVideoWriter::WriteFrame(void* cudaBuffer, size_t bufferSize, size_t bufferPitch) {
    if (!m_isInitialized || !cudaBuffer || m_isFinalized) return false;
    try {
        if (!PrepareFrame()) return false;
        if (!TransferCudaToFrame(cudaBuffer, bufferSize, bufferPitch)) return false;
        return EncodeFrame();
    }
    catch (const std::exception& e) {
        std::cerr << "Error writing frame: " << e.what() << std::endl;
        return false;
    }
}

bool DirectVideoWriter::EncodeFrame() {
    int ret = avcodec_send_frame(m_codecContext, m_hwFrame);
    if (ret < 0) {
        char err_buf[AV_ERROR_MAX_STRING_SIZE];
        av_make_error_string(err_buf, AV_ERROR_MAX_STRING_SIZE, ret);
        std::cerr << "Error sending frame to encoder: " << err_buf << std::endl;
        return false;
    }

    while (ret >= 0) {
        ret = avcodec_receive_packet(m_codecContext, m_packet);
        if (ret == AVERROR(EAGAIN) || ret == AVERROR_EOF) break;
        if (ret < 0) return false;
        if (av_interleaved_write_frame(m_formatContext, m_packet) < 0) {
            throw std::runtime_error("Error writing packet to file.");
        }
        av_packet_unref(m_packet);
    }
    return true;
}

bool DirectVideoWriter::Finalize() {
    if (!m_isInitialized || m_isFinalized) return false;
    if (m_cudaStream) cudaStreamSynchronize(m_cudaStream);
    int ret = avcodec_send_frame(m_codecContext, nullptr);
    if (ret < 0) return false;
    while (ret >= 0) {
        ret = avcodec_receive_packet(m_codecContext, m_packet);
        if (ret == AVERROR_EOF) break;
        if (ret < 0) return false;
        if (av_interleaved_write_frame(m_formatContext, m_packet) < 0) return false;
        av_packet_unref(m_packet);
    }
    if (av_write_trailer(m_formatContext) < 0) return false;
    m_isFinalized = true;
    return true;
}

void DirectVideoWriter::Close() {
    if (m_isInitialized && !m_isFinalized) Finalize();
    m_isInitialized = false;

    if (m_codecContext) avcodec_free_context(&m_codecContext);
    if (m_hwFrame) av_frame_free(&m_hwFrame);
    if (m_swFrame) av_frame_free(&m_swFrame);
    if (m_packet) av_packet_free(&m_packet);

    if (m_formatContext) {
        if (!(m_formatContext->oformat->flags & AVFMT_NOFILE) && m_formatContext->pb) {
            avio_closep(&m_formatContext->pb);
        }
        avformat_free_context(m_formatContext);
        m_formatContext = nullptr;
    }

    if (m_cudaStream) cudaStreamDestroy(m_cudaStream);
    if (m_encoderStream) cudaStreamDestroy(m_encoderStream);
    m_cudaStream = nullptr;
    m_encoderStream = nullptr;
    m_cudaContext = nullptr;

    if (m_frameRate) {
        DeleteAVRational(m_frameRate);
        m_frameRate = nullptr;
    }
}