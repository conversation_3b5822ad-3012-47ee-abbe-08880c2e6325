#pragma once

#define NOMINMAX // Disable the min/max macros from windows.h which interfere with std::min/max

#include "ImageMattingOnnx.h"
#include "lodepng.h"
#include <wincodec.h>
#include <algorithm>
#include <iostream>
// DirectX headers removed
#include <wrl/client.h>
#include "Helpers.h"
#include "StringUtils.h"
#include <cuda.h>
#include <cuda_runtime.h>
#include <vector>
#include <memory>
#include <locale>
#include <filesystem>

// Include FFmpeg headers for implementation
extern "C" {
#include <libavutil/rational.h>
}

using Microsoft::WRL::ComPtr;

// Helper function to convert wstring to string safely
std::string WideToUtf8(const std::wstring& wstr) {
    if (wstr.empty()) return std::string();

    // Get the required buffer size
    int size_needed = WideCharToMultiByte(CP_UTF8, 0, wstr.data(), (int)wstr.size(), NULL, 0, NULL, NULL);

    // Allocate the string of required size
    std::string result(size_needed, 0);

    // Do the actual conversion
    WideCharToMultiByte(CP_UTF8, 0, wstr.data(), (int)wstr.size(), &result[0], size_needed, NULL, NULL);

    return result;
}

// Save an alpha matte (float array) as a grayscale PNG
bool SaveAlphaMatteToPNG(const char* filename, const float* alphaMatteOnGpu, int width, int height) {
    // Allocate buffer for the 8-bit grayscale image
    std::vector<unsigned char> image(width * height);

    // Copy to CPU and convert from float [0,1] to byte [0,255]
    float* hostAlpha = new float[width * height];
    cudaMemcpy(hostAlpha, alphaMatteOnGpu, width * height * sizeof(float), cudaMemcpyDeviceToHost);

    for (int i = 0; i < width * height; i++) {
        image[i] = (unsigned char)(std::max(0.0f, std::min(1.0f, hostAlpha[i])) * 255.0f);
    }

    delete[] hostAlpha;

    // Ensure parent directory exists
    {
        std::filesystem::path outPath(filename);
        if (outPath.has_parent_path()) {
            std::error_code ec;
            std::filesystem::create_directories(outPath.parent_path(), ec);
        }
    }
    // Save using LodePNG
    unsigned error = lodepng::encode(filename, image, width, height, LCT_GREY);

    if (error) {
        std::cerr << "Error saving alpha matte PNG: " << lodepng_error_text(error) << std::endl;
        return false;
    }

    std::cout << "Saved alpha matte to " << filename << std::endl;
    return true;
}

// Save an alpha matte (unsigned char array) as a grayscale PNG
bool SaveAlphaMatteToPNG(const char* filename, const unsigned char* alphaMatteOnCpu, int width, int height) {

    // Save using LodePNG
    unsigned error = lodepng::encode(filename, alphaMatteOnCpu, width, height, LCT_GREY);

    if (error) {
        std::cerr << "Error saving alpha matte PNG: " << lodepng_error_text(error) << std::endl;
        return false;
    }

    std::cout << "Saved alpha matte to " << filename << std::endl;
    return true;
}

// Save an RGBA or RGB image (float array in interleaved format) as a PNG
bool SaveInterleavedFloatImageToPNG(const char* filename, const float* imageData,
    int width, int height, bool hasAlpha) {
    // Determine channel count and output format
    const int inputChannels = hasAlpha ? 4 : 3;
    const LodePNGColorType outputColorType = hasAlpha ? LCT_RGBA : LCT_RGB;

    // Allocate buffer for the 8-bit image
    std::vector<unsigned char> image(width * height * (hasAlpha ? 4 : 3));

    // Copy to CPU
    float* hostImage = new float[width * height * inputChannels];
    cudaMemcpy(hostImage, imageData, width * height * inputChannels * sizeof(float),
        cudaMemcpyDeviceToHost);

    // Convert float to byte
    for (int y = 0; y < height; y++) {
        for (int x = 0; x < width; x++) {
            int srcIdx = (y * width + x) * inputChannels;
            int dstIdx = (y * width + x) * inputChannels;

            // RGB components (always present)
            image[dstIdx + 0] = (unsigned char)(std::max(0.0f, std::min(1.0f, hostImage[srcIdx + 0])) * 255.0f);
            image[dstIdx + 1] = (unsigned char)(std::max(0.0f, std::min(1.0f, hostImage[srcIdx + 1])) * 255.0f);
            image[dstIdx + 2] = (unsigned char)(std::max(0.0f, std::min(1.0f, hostImage[srcIdx + 2])) * 255.0f);

            // Alpha component if available
            if (hasAlpha) {
                image[dstIdx + 3] = (unsigned char)(std::max(0.0f, std::min(1.0f, hostImage[srcIdx + 3])) * 255.0f);
            }
        }
    }

    delete[] hostImage;

    // Save using LodePNG
    unsigned error = lodepng::encode(filename, image, width, height, outputColorType);

    if (error) {
        std::cerr << "Error saving " << (hasAlpha ? "RGBA" : "RGB")
            << " PNG: " << lodepng_error_text(error) << std::endl;
        return false;
    }

    std::cout << "Saved " << (hasAlpha ? "RGBA" : "RGB")
        << " image to " << filename << std::endl;
    return true;
}

// Saves a GPU-resident planar float RGB/RGBA buffer to a PNG file
cudaError_t SavePlanarFloatImageToPNG(const std::string& filename,
    const float* deviceImageData,
    int width, int height,
    bool hasAlpha,
    cudaStream_t stream) {
    // Determine channel count and parameters
    const int channels = hasAlpha ? 4 : 3;
    const LodePNGColorType outputColorType = hasAlpha ? LCT_RGBA : LCT_RGB;
    const int outputChannels = hasAlpha ? 4 : 3;
    const size_t planeSize = width * height;

    // Allocate host memory for the image data
    std::unique_ptr<float[]> hostImage(new float[planeSize * channels]);

    // Copy data from device to host
    cudaError_t cudaStatus = cudaMemcpyAsync(hostImage.get(), deviceImageData,
        planeSize * channels * sizeof(float),
        cudaMemcpyDeviceToHost,
        stream);
    if (cudaStatus != cudaSuccess) {
        std::cerr << "Failed to copy image data from device: "
            << cudaGetErrorString(cudaStatus) << std::endl;
        return cudaStatus;
    }

    // Synchronize to ensure the copy is complete
    if (stream) {
        cudaStatus = cudaStreamSynchronize(stream);
        if (cudaStatus != cudaSuccess) {
            std::cerr << "Failed to synchronize stream: "
                << cudaGetErrorString(cudaStatus) << std::endl;
            return cudaStatus;
        }
    }

    // Allocate buffer for the 8-bit image (for lodepng)
    std::vector<unsigned char> image(planeSize * outputChannels);

    // Convert planar float to interleaved byte
    for (int y = 0; y < height; y++) {
        for (int x = 0; x < width; x++) {
            const int pixelIdx = y * width + x;
            const int outIdx = pixelIdx * outputChannels;

            // Get channel values (planar format)
            float r = hostImage[pixelIdx];                    // R plane
            float g = hostImage[planeSize + pixelIdx];        // G plane
            float b = hostImage[2 * planeSize + pixelIdx];    // B plane

            // Convert to byte with proper rounding
            image[outIdx + 0] = static_cast<unsigned char>(std::round(std::max(0.0f, std::min(1.0f, r)) * 255.0f));
            image[outIdx + 1] = static_cast<unsigned char>(std::round(std::max(0.0f, std::min(1.0f, g)) * 255.0f));
            image[outIdx + 2] = static_cast<unsigned char>(std::round(std::max(0.0f, std::min(1.0f, b)) * 255.0f));

            // Handle alpha channel if present
            if (hasAlpha) {
                float a = hostImage[3 * planeSize + pixelIdx];  // A plane
                image[outIdx + 3] = static_cast<unsigned char>(std::round(std::max(0.0f, std::min(1.0f, a)) * 255.0f));
            }
        }
    }

    // Save using LodePNG
    unsigned error = lodepng::encode(filename, image, width, height, outputColorType);

    if (error) {
        std::cerr << "Error saving " << (hasAlpha ? "RGBA" : "RGB")
            << " PNG: " << lodepng_error_text(error) << std::endl;
        return cudaErrorUnknown;
    }

    std::cout << "Saved " << (hasAlpha ? "RGBA" : "RGB")
        << " image to " << filename << std::endl;
    return cudaSuccess;
}

std::string ConvertWCharToChar(const wchar_t* wcharStr) {
    if (!wcharStr) return "";

    // Get the required buffer size
    int size_needed = WideCharToMultiByte(CP_UTF8, 0, wcharStr, -1, nullptr, 0, nullptr, nullptr);
    if (size_needed <= 0) return "";

    // Allocate the buffer and convert
    std::string strTo(size_needed, 0);
    WideCharToMultiByte(CP_UTF8, 0, wcharStr, -1, &strTo[0], size_needed, nullptr, nullptr);
    strTo.resize(size_needed - 1);  // Remove the null terminator included in the size

    return strTo;
}

std::string ConvertWCharToChar(const std::wstring& wstr) {
    if (wstr.empty()) return "";
    return ConvertWCharToChar(wstr.c_str());
}

std::wstring ConvertCharToWChar(const std::string& str) {
    if (str.empty()) return L"";

    // Get the required buffer size
    int size_needed = MultiByteToWideChar(CP_UTF8, 0, str.c_str(), -1, nullptr, 0);
    if (size_needed <= 0) return L"";

    // Allocate the buffer and convert
    std::wstring wstrTo(size_needed, 0);
    MultiByteToWideChar(CP_UTF8, 0, str.c_str(), -1, &wstrTo[0], size_needed);
    wstrTo.resize(size_needed - 1);  // Remove the null terminator included in the size

    return wstrTo;
}

Ort::Value CreateOrtValueFromDeviceMemory(void* deviceBuffer, const std::vector<int64_t>& shape,
    ONNXTensorElementDataType dataType, const Ort::MemoryInfo& memoryInfo) {
    size_t elementSize;
    switch (dataType) {
    case ONNX_TENSOR_ELEMENT_DATA_TYPE_FLOAT:
        elementSize = sizeof(float);
        break;
        // Add more cases as needed
    default:
        throw std::runtime_error("Unsupported tensor data type");
    }

    size_t totalSize = elementSize;
    for (const auto& dim : shape) {
        totalSize *= dim;
    }

    return Ort::Value::CreateTensor(memoryInfo, deviceBuffer, totalSize,
        shape.data(), shape.size(), dataType);
}

bool loadRGBAImageToCudaBufferFloatPlanar(const char* rgbImagePath, const char* alphaMaskPath,
    float* cudaBuffer, int expectedWidth, int expectedHeight,
    cudaStream_t stream) {
    std::vector<unsigned char> rgbImage;
    std::vector<unsigned char> alphaImage;
    unsigned int rgbWidth, rgbHeight;
    unsigned int alphaWidth, alphaHeight;

    // Load RGB image
    unsigned int error = lodepng::decode(rgbImage, rgbWidth, rgbHeight, rgbImagePath);
    if (error) {
        std::cerr << "Error loading RGB image: " << lodepng_error_text(error) << std::endl;
        return false;
    }

    // Load alpha mask
    error = lodepng::decode(alphaImage, alphaWidth, alphaHeight, alphaMaskPath);
    if (error) {
        std::cerr << "Error loading alpha mask: " << lodepng_error_text(error) << std::endl;
        return false;
    }

    // Check dimensions
    if (rgbWidth != alphaWidth || rgbHeight != alphaHeight) {
        std::cerr << "RGB image and alpha mask dimensions don't match!" << std::endl;
        return false;
    }

    if ((int)rgbWidth != expectedWidth || (int)rgbHeight != expectedHeight) {
        std::cerr << "Image dimensions don't match expected size!" << std::endl;
        return false;
    }

    // Create planar RGBA buffer in host memory
    size_t pixelCount = expectedWidth * expectedHeight;
    std::vector<float> rgbaFloatPlanar(pixelCount * 4);

    // Determine channel counts
    size_t rgbChannels = rgbImage.size() / pixelCount;
    size_t alphaChannels = alphaImage.size() / pixelCount;

    // Convert to planar float format (0-1 range)
    // Layout: [R plane][G plane][B plane][A plane]
    for (size_t i = 0; i < pixelCount; ++i) {
        // Red channel
        if (rgbChannels >= 3) {
            rgbaFloatPlanar[i] = rgbImage[i * rgbChannels + 0] / 255.0f;
        }
        else {
            rgbaFloatPlanar[i] = 0.0f;
        }

        // Green channel
        if (rgbChannels >= 3) {
            rgbaFloatPlanar[pixelCount + i] = rgbImage[i * rgbChannels + 1] / 255.0f;
        }
        else {
            rgbaFloatPlanar[pixelCount + i] = 0.0f;
        }

        // Blue channel
        if (rgbChannels >= 3) {
            rgbaFloatPlanar[2 * pixelCount + i] = rgbImage[i * rgbChannels + 2] / 255.0f;
        }
        else {
            rgbaFloatPlanar[2 * pixelCount + i] = 0.0f;
        }

        // Alpha channel from mask
        if (alphaChannels >= 1) {
            rgbaFloatPlanar[3 * pixelCount + i] = alphaImage[i * alphaChannels] / 255.0f;
        }
        else {
            rgbaFloatPlanar[3 * pixelCount + i] = 1.0f; // Default full opacity
        }
    }

    // Copy data to pre-allocated CUDA buffer
    size_t bufferSize = pixelCount * 4 * sizeof(float);
    cudaError_t cudaErr = cudaMemcpyAsync(cudaBuffer, rgbaFloatPlanar.data(), bufferSize, 
        cudaMemcpyHostToDevice, stream);
    if (cudaErr != cudaSuccess) {
        std::cerr << "CUDA memcpy failed: " << cudaGetErrorString(cudaErr) << std::endl;
        return false;
    }

    // Synchronize to ensure the copy is complete
    if (stream) {
        cudaErr = cudaStreamSynchronize(stream);
        if (cudaErr != cudaSuccess) {
            std::cerr << "Failed to synchronize stream: " << cudaGetErrorString(cudaErr) << std::endl;
            return false;
        }
    }

    return true;
}

std::string ConvertWCharToString(const wchar_t* wstr) {
    if (!wstr) return std::string();
    std::wstring ws(wstr);
    return ConvertWStringToString(ws);
}

std::wstring ConvertStringToWString(const std::string& str) {
    return ConvertCharToWChar(str);
}

std::string ConvertWStringToString(const std::wstring& wstr) {
    return ConvertWCharToChar(wstr);
}

