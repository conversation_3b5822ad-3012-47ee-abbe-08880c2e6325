#include "alpha_codec.h"
#include "alpha_codec.cuh"
#include <cuda_runtime.h>
#include <cstdlib>
#include <cstring>
#include <algorithm>

// Include the kernel launch declarations
#include "alpha_codec.cuh"

// Simple compression function for the final output (placeholder for Brotli)
size_t simple_compress(const void* input, size_t input_size, void** output) {
	// In a real implementation, you would use Brotli compression here
	// For now, just copy the data (no compression)
	*output = malloc(input_size + sizeof(size_t));
	if (!*output)
		return 0;

	// Store original size at the beginning
	memcpy(*output, &input_size, sizeof(size_t));
	memcpy((char*)*output + sizeof(size_t), input, input_size);

	return input_size + sizeof(size_t);
}

// Simple decompression function (placeholder for Brotli)
size_t simple_decompress(const void* input, size_t input_size, void** output, size_t expected_size) {
	if (input_size < sizeof(size_t))
		return 0;

	size_t original_size;
	memcpy(&original_size, input, sizeof(size_t));

	if (original_size != expected_size)
		return 0;

	*output = malloc(original_size);
	if (!*output)
		return 0;

	memcpy(*output, (const char*)input + sizeof(size_t), original_size);
	return original_size;
}

CompressedAlphaData* compress_alpha_cuda(const uint8_t* d_alpha_input,
	uint32_t width,
	uint32_t height,
	cudaStream_t stream) {
	cudaError_t err;

	// Allocate device memory
	int* d_column_headers = nullptr;
	int* d_output1 = nullptr;
	int* d_output2 = nullptr;

	err = cudaMalloc(&d_column_headers, width * sizeof(int));
	if (err != cudaSuccess)
		goto cleanup;

	err = cudaMalloc(&d_output1, width * height * sizeof(int));
	if (err != cudaSuccess)
		goto cleanup;

	err = cudaMalloc(&d_output2, width * height * sizeof(int));
	if (err != cudaSuccess)
		goto cleanup;

	// Launch compression kernels
	launch_compress_columns(d_alpha_input, width, height, d_column_headers, d_output1, stream);
	launch_add_column_headers(d_column_headers, width, stream);
	launch_compress_lines(d_column_headers, d_output1, d_output2, width, stream);

	// Wait for kernels to complete
	err = cudaStreamSynchronize(stream);
	if (err != cudaSuccess)
		goto cleanup;

	// Copy results back to host
	int* h_column_headers = new int[width];
	err = cudaMemcpy(h_column_headers, d_column_headers, width * sizeof(int), cudaMemcpyDeviceToHost);
	if (err != cudaSuccess) {
		delete[] h_column_headers;
		goto cleanup;
	}

	uint32_t alpha_length = h_column_headers[width - 1];
	int* h_alpha = new int[alpha_length];
	err = cudaMemcpy(h_alpha, d_output2, alpha_length * sizeof(int), cudaMemcpyDeviceToHost);
	if (err != cudaSuccess) {
		delete[] h_column_headers;
		delete[] h_alpha;
		goto cleanup;
	}

	// Create compressed data structure
	CompressedAlphaData* compressed = new CompressedAlphaData();
	compressed->width = width;
	compressed->height = height;
	compressed->alpha_length = alpha_length;

	// Compress column headers and alpha data
	void* compressed_headers = nullptr;
	void* compressed_alpha = nullptr;

	size_t compressed_headers_size = simple_compress(h_column_headers, width * sizeof(int), &compressed_headers);
	size_t compressed_alpha_size = simple_compress(h_alpha, alpha_length * sizeof(int), &compressed_alpha);

	if (compressed_headers_size == 0 || compressed_alpha_size == 0) {
		delete[] h_column_headers;
		delete[] h_alpha;
		delete compressed;
		if (compressed_headers)
			free(compressed_headers);
		if (compressed_alpha)
			free(compressed_alpha);
		goto cleanup;
	}

	// Pack everything into a single buffer
	compressed->compressed_size = sizeof(uint32_t) * 4 + compressed_headers_size + compressed_alpha_size;
	compressed->compressed_data = (uint8_t*)malloc(compressed->compressed_size);

	if (!compressed->compressed_data) {
		delete[] h_column_headers;
		delete[] h_alpha;
		delete compressed;
		free(compressed_headers);
		free(compressed_alpha);
		goto cleanup;
	}

	// Pack data: [width][height][alpha_length][headers_size][headers_data][alpha_data]
	uint8_t* ptr = compressed->compressed_data;
	memcpy(ptr, &width, sizeof(uint32_t));
	ptr += sizeof(uint32_t);
	memcpy(ptr, &height, sizeof(uint32_t));
	ptr += sizeof(uint32_t);
	memcpy(ptr, &alpha_length, sizeof(uint32_t));
	ptr += sizeof(uint32_t);
	memcpy(ptr, &compressed_headers_size, sizeof(uint32_t));
	ptr += sizeof(uint32_t);
	memcpy(ptr, compressed_headers, compressed_headers_size);
	ptr += compressed_headers_size;
	memcpy(ptr, compressed_alpha, compressed_alpha_size);

	// Cleanup
	delete[] h_column_headers;
	delete[] h_alpha;
	free(compressed_headers);
	free(compressed_alpha);

cleanup:
	if (d_column_headers)
		cudaFree(d_column_headers);
	if (d_output1)
		cudaFree(d_output1);
	if (d_output2)
		cudaFree(d_output2);

	return (err == cudaSuccess) ? compressed : nullptr;
}

cudaError_t decompress_alpha_cuda(const CompressedAlphaData* compressed_data,
	uint8_t* d_alpha_output,
	cudaStream_t stream) {
	if (!compressed_data || !compressed_data->compressed_data || !d_alpha_output) {
		return cudaErrorInvalidValue;
	}

	// Unpack compressed data
	const uint8_t* ptr = compressed_data->compressed_data;
	uint32_t width, height, alpha_length, headers_size;

	memcpy(&width, ptr, sizeof(uint32_t));
	ptr += sizeof(uint32_t);
	memcpy(&height, ptr, sizeof(uint32_t));
	ptr += sizeof(uint32_t);
	memcpy(&alpha_length, ptr, sizeof(uint32_t));
	ptr += sizeof(uint32_t);
	memcpy(&headers_size, ptr, sizeof(uint32_t));
	ptr += sizeof(uint32_t);

	// Decompress column headers and alpha data
	void* h_column_headers = nullptr;
	void* h_alpha = nullptr;

	size_t decompressed_headers_size = simple_decompress(ptr, headers_size, &h_column_headers, width * sizeof(int));
	ptr += headers_size;

	size_t alpha_data_size = compressed_data->compressed_size - sizeof(uint32_t) * 4 - headers_size;
	size_t decompressed_alpha_size = simple_decompress(ptr, alpha_data_size, &h_alpha, alpha_length * sizeof(int));

	if (decompressed_headers_size == 0 || decompressed_alpha_size == 0) {
		if (h_column_headers)
			free(h_column_headers);
		if (h_alpha)
			free(h_alpha);
		return cudaErrorInvalidValue;
	}

	// Allocate device memory
	int* d_column_headers = nullptr;
	int* d_input = nullptr;

	cudaError_t err = cudaMalloc(&d_column_headers, width * sizeof(int));
	if (err != cudaSuccess)
		goto cleanup;

	err = cudaMalloc(&d_input, alpha_length * sizeof(int));
	if (err != cudaSuccess)
		goto cleanup;

	// Copy data to device
	err = cudaMemcpy(d_column_headers, h_column_headers, width * sizeof(int), cudaMemcpyHostToDevice);
	if (err != cudaSuccess)
		goto cleanup;

	err = cudaMemcpy(d_input, h_alpha, alpha_length * sizeof(int), cudaMemcpyHostToDevice);
	if (err != cudaSuccess)
		goto cleanup;

	// Launch decompression kernel
	launch_decompress(d_column_headers, d_input, d_alpha_output, width, height, stream);

	// Wait for completion
	err = cudaStreamSynchronize(stream);

cleanup:
	if (h_column_headers)
		free(h_column_headers);
	if (h_alpha)
		free(h_alpha);
	if (d_column_headers)
		cudaFree(d_column_headers);
	if (d_input)
		cudaFree(d_input);

	return err;
}

void free_compressed_alpha_data(CompressedAlphaData* data) {
	if (data) {
		delete data;
	}
}