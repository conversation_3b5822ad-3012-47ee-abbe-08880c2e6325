#pragma once

#include "ImageMatting.h"
#include <memory>
#include <string>
#include <vector>
#include <map>
#include <mutex>
#include <regex>

/**
 * Enumeration of supported model types
 */
enum class ModelType {
    INSPYRENET,     // InsPyReNet models with multiple sizes and aspect ratio selection
    INDEXNET,       // IndexNet models with multiple sizes, selects smallest that fits
    RMBG1_4,        // RMBG 1.4 models with fixed size
    RMBG2_0           // RMBG 2.0 models with fixed size
};

/**
 * Enumeration of inference backend preferences
 */
enum class InferenceBackend {
    AUTO,           // Use existing TensorRT engines if available, otherwise use ONNX (no automatic engine creation)
    ONNX_ONLY,      // Use ONNX Runtime only
    TENSORRT_ONLY,  // Use TensorRT only (create engine with 2048 MB pool if missing, fail if TensorRT unavailable)
    TENSORRT_WITH_FALLBACK  // Prefer TensorRT (create engine with 2048 MB pool if missing), fallback to ONNX if TensorRT fails
};

/**
 * Structure to hold model information
 */
struct ModelInfo {
    std::wstring path;
    ModelType type;
    int width;
    int height;
    float aspectRatio;

    ModelInfo() : type(ModelType::INSPYRENET), width(0), height(0), aspectRatio(1.0f) {}
    ModelInfo(const std::wstring& p, ModelType t, int w, int h)
        : path(p), type(t), width(w), height(h), aspectRatio(static_cast<float>(w) / h) {}
};

/**
 * Configuration for each model type
 */
struct ModelTypeConfig {
    std::regex filenamePattern;
    NormalizationParams defaultNormalization;
    bool isRgba;
    bool hasMultipleSizes;
    ResizeMethod resizeMethod;
    std::vector<std::string> trtexecArgs;  // Additional arguments for trtexec
    int poolSizeMB;  // Memory pool size in MB for TensorRT engine creation
    bool isMixedPrecision;  // Whether the ONNX model is mixed precision and FP32 layers should be preserved

    // Default constructor
    ModelTypeConfig() : isRgba(false), hasMultipleSizes(false), resizeMethod(ResizeMethod::EXTEND_SHRINK_LANCZOS), poolSizeMB(2048), isMixedPrecision(false) {}

    ModelTypeConfig(const std::string& pattern,
                   const NormalizationParams& norm, bool rgba, bool multiSize,
                   ResizeMethod resize = ResizeMethod::EXTEND_SHRINK_LANCZOS,
                   const std::vector<std::string>& trtArgs = {},
                   int poolSize = 2048,
                   bool mixedPrecision = false)
        : filenamePattern(pattern, std::regex_constants::icase), defaultNormalization(norm),
          isRgba(rgba), hasMultipleSizes(multiSize), resizeMethod(resize), trtexecArgs(trtArgs), poolSizeMB(poolSize), isMixedPrecision(mixedPrecision) {}
};

/**
 * Structure to hold instance information for pooling
 */
struct MattingInstance {
    ModelType type;
    int width;
    int height;
    cudaStream_t stream;
    std::unique_ptr<ImageMatting> imageMatting;
    bool inUse;

    MattingInstance() : type(ModelType::INSPYRENET), width(0), height(0), stream(nullptr), inUse(false) {}
};

/**
 * Factory class for creating ImageMatting instances
 * Provides a unified interface with model type-based initialization,
 * automatic model selection, and instance pooling
 */
class ImageMattingFactory {
public:
    enum class BestModelSelectionMethod {
        DEFAULT,                        // Use current logic (per-model-type)
        ASPECT_RATIO_CLOSEST_FIT,       // Match aspect ratio, then closest size (same as previous ASPECT_RATIO)
        ASPECT_RATIO_CLOSEST_BIGGER_SIZE, // Match aspect ratio, then closest bigger size
        SMALLEST_FIT,                   // Smallest model >= image size (IndexNet style)
        FIXED_SIZE,                     // Just pick the first (for fixed-size models)
        SMALLEST_AREA_FIT               // New: Smallest area model that fits image
    };
    /**
     * Initialize an ImageMatting instance with automatic format detection
     * This is the main function that should be used everywhere ImageMatting is needed
     *
     * @param modelPath Path to the model file (.onnx for ONNX Runtime, .trt/.engine for TensorRT)
     * @param modelWidth Width of the model input (required)
     * @param modelHeight Height of the model input (required)
     * @param imageWidth Width of the images that will be processed
     * @param imageHeight Height of the images that will be processed
     * @param normParams Normalization parameters for preprocessing
     * @param isRgba Whether the input buffer will be in RGBA format
     * @param externalStream CUDA stream to use for operations
     * @return Unique pointer to ImageMatting implementation, or nullptr on failure
     */
    /**
     * NEW UNIFIED INTERFACE: Get an ImageMatting instance by model type
     * This is the new main function that should be used everywhere ImageMatting is needed
     * Returns a managed instance that should be returned using ReturnInstance when done
     *
     * @param modelType Type of model to initialize
     * @param imageWidth Width of the images that will be processed
     * @param imageHeight Height of the images that will be processed
     * @param stream CUDA stream to use for operations (optional)
     * @return Raw pointer to ImageMatting implementation, or nullptr on failure
     */
    static ImageMatting* GetInstance(
        ModelType modelType,
        int imageWidth,
        int imageHeight,
        cudaStream_t stream = nullptr,
        InferenceBackend backend = InferenceBackend::AUTO,
        BestModelSelectionMethod selectionMethod = BestModelSelectionMethod::DEFAULT,
        bool useModelSize = false);

    static ImageMatting* GetInstance(
        ModelType modelType,
        int imageWidth,
        int imageHeight,
        const NormalizationParams& normParams,
        cudaStream_t stream = nullptr,
        InferenceBackend backend = InferenceBackend::AUTO,
        BestModelSelectionMethod selectionMethod = BestModelSelectionMethod::DEFAULT,
        bool useModelSize = false);

    static std::unique_ptr<ImageMatting> Init(
        ModelType modelType,
        int imageWidth,
        int imageHeight,
        cudaStream_t stream = nullptr,
        InferenceBackend backend = InferenceBackend::AUTO,
        BestModelSelectionMethod selectionMethod = BestModelSelectionMethod::DEFAULT,
        bool useModelSize = false);

    static std::unique_ptr<ImageMatting> Init(
        ModelType modelType,
        int imageWidth,
        int imageHeight,
        const NormalizationParams& normParams,
        cudaStream_t stream = nullptr,
        InferenceBackend backend = InferenceBackend::AUTO,
        BestModelSelectionMethod selectionMethod = BestModelSelectionMethod::DEFAULT,
        bool useModelSize = false);

    /**
     * Return an instance when done using it (for instance pooling)
     * @param instance The instance to return to the pool
     */
    static void ReturnInstance(ImageMatting* instance);


    /**
     * Force ONNX Runtime implementation
     * @param modelPath Path to the ONNX model file
     * @param modelWidth Width of the model input (required)
     * @param modelHeight Height of the model input (required)
     * @param imageWidth Width of the images that will be processed
     * @param imageHeight Height of the images that will be processed
     * @param normParams Normalization parameters for preprocessing
     * @param isRgba Whether the input buffer will be in RGBA format
     * @param externalStream CUDA stream to use for operations
     * @return Unique pointer to ImageMatting implementation, or nullptr on failure
     */
    static std::unique_ptr<ImageMatting> InitOnnx(
        const wchar_t* modelPath,
        int modelWidth,
        int modelHeight,
        int imageWidth,
        int imageHeight,
        const NormalizationParams& normParams,
        bool isRgba = false,
        ResizeMethod resizeMethod = ResizeMethod::EXTEND_SHRINK_LANCZOS,
        cudaStream_t externalStream = nullptr);

    /**
     * Force TensorRT implementation
     * @param enginePath Path to the TensorRT engine file
     * @param modelWidth Width of the model input (required)
     * @param modelHeight Height of the model input (required)
     * @param imageWidth Width of the images that will be processed
     * @param imageHeight Height of the images that will be processed
     * @param normParams Normalization parameters for preprocessing
     * @param isRgba Whether the input buffer will be in RGBA format
     * @param externalStream CUDA stream to use for operations
     * @return Unique pointer to ImageMattingTensorRt implementation, or nullptr on failure
     */
    static std::unique_ptr<ImageMatting> InitTensorRt(
        const wchar_t* enginePath,
        int modelWidth,
        int modelHeight,
        int imageWidth,
        int imageHeight,
        const NormalizationParams& normParams,
        bool isRgba = false,
        ResizeMethod resizeMethod = ResizeMethod::EXTEND_SHRINK_LANCZOS,
        cudaStream_t externalStream = nullptr);

    /**
     * Create TensorRT engines for all available ONNX models
     * Uses model-specific pool sizes from ModelTypeConfig
     * @param forceRecreate If true, recreate engines even if they already exist
     * @return Number of engines successfully created
     */
     static int CreateAllTensorRtEngines(bool forceRecreate = false);

private:
    // Static data for model management
    static std::map<ModelType, ModelTypeConfig> s_modelConfigs;
    static std::vector<ModelInfo> s_availableModels;
    static std::map<std::tuple<ModelType, int, int, int, int, InferenceBackend, cudaStream_t>, std::vector<std::unique_ptr<MattingInstance>>> s_instances;
    static std::mutex s_instancesMutex;
    static bool s_modelsScanned;
    static std::mutex s_scanMutex;

    // Model management functions
    static void InitializeModelConfigs();
    static void ScanAvailableModels();

    static ModelInfo FindBestModel(ModelType modelType, int imageWidth, int imageHeight, BestModelSelectionMethod method = BestModelSelectionMethod::DEFAULT, InferenceBackend backend = InferenceBackend::AUTO);
    static std::unique_ptr<ImageMatting> CreateInstance(const ModelInfo& modelInfo, int imageWidth, int imageHeight,
                                                       const NormalizationParams& normParams, cudaStream_t stream, InferenceBackend backend);

    // Legacy helper functions
    static std::wstring GetFileExtension(const wchar_t* filePath);
    static bool FileExists(const wchar_t* filePath);
    static bool CreateTensorRtEngine(const wchar_t* onnxPath, const wchar_t* enginePath, int poolSizeMB, const std::vector<std::string>& additionalArgs = {});
    static bool CreateTensorRtEngine(const wchar_t* onnxPath, const wchar_t* enginePath, int poolSizeMB, const std::vector<std::string>& additionalArgs, bool isMixedPrecision);

    // Helper functions for trtexec-based engine creation
    static bool ExecuteTrtexecCommand(const std::vector<std::string>& cmdArgs, bool isTest = false, int timeoutMs = 3000000); // 50 minute default timeout
    static std::vector<std::pair<ModelInfo, bool>> GetModelEngineStatus();
};
