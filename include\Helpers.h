#pragma once

#include <string>
#include <stdexcept>
#include <cuda_runtime.h>
#include <vector>
#define WIN32_LEAN_AND_MEAN
#include <windows.h>
#include <onnxruntime_cxx_api.h>

// Error checking for CUDA calls
#define CUDA_CHECK(call) \
    do { \
        cudaError_t error = call; \
        if (error != cudaSuccess) { \
            std::cerr << "CUDA error at " << __FILE__ << ":" << __LINE__ << " - " \
                      << cudaGetErrorString(error) << std::endl; \
            return 1; \
        } \
    } while(0)

// Forward declarations for FFmpeg types
extern "C" {
    #include <libavutil/rational.h>
    #include <libavutil/error.h>
    #include <libavutil/mem.h>
}

// Forward declarations for CUDA types
//typedef struct CUcontext_st* CUcontext;

namespace VideoUtils {
    // Texture format enumeration
    enum class TextureFormat {
        Unknown,
        NV12,
        YUV420P,
        RGBA,
        BGRA
    };

    // String conversion functions
    inline std::string WStringToString(const std::wstring& wstr) {
        if (wstr.empty()) return std::string();
        int size_needed = WideCharToMultiByte(CP_UTF8, 0, wstr.c_str(), (int)wstr.size(), nullptr, 0, nullptr, nullptr);
        std::string str(size_needed, 0);
        WideCharToMultiByte(CP_UTF8, 0, wstr.c_str(), (int)wstr.size(), &str[0], size_needed, nullptr, nullptr);
        return str;
    }

    inline std::wstring StringToWString(const std::string& str) {
        if (str.empty()) return std::wstring();
        int size_needed = MultiByteToWideChar(CP_UTF8, 0, str.c_str(), (int)str.size(), nullptr, 0);
        std::wstring wstr(size_needed, 0);
        MultiByteToWideChar(CP_UTF8, 0, str.c_str(), (int)str.size(), &wstr[0], size_needed);
        return wstr;
    }

    // AVRational helper functions
    inline AVRational* CreateAVRational(int num, int den) {
        AVRational* r = (AVRational*)av_malloc(sizeof(AVRational));
        if (r) {
            r->num = num;
            r->den = den;
        }
        return r;
    }

    inline void DeleteAVRational(AVRational* r) {
        if (r) {
            av_free(r);
        }
    }

    // CUDA error checking
    inline void CheckCudaError(cudaError_t err) {
        if (err != cudaSuccess) {
            throw std::runtime_error(std::string("CUDA error: ") + cudaGetErrorString(err));
        }
    }

    // FFmpeg error checking
    inline void CheckFFmpegError(int err) {
        if (err < 0) {
            char errbuf[AV_ERROR_MAX_STRING_SIZE] = { 0 };
            av_strerror(err, errbuf, AV_ERROR_MAX_STRING_SIZE);
            throw std::runtime_error(std::string("FFmpeg error: ") + errbuf);
        }
    }
}

extern "C" {
#include <libavutil/rational.h>
}

bool SaveAlphaMatteToPNG(const char* filename, const float* alphaMatteOnGpu, int width, int height);
bool SaveAlphaMatteToPNG(const char* filename, const unsigned char* alphaMatteOnCpu, int width, int height);
bool SaveInterleavedFloatImageToPNG(const char* filename, const float* rgbaImage, int width, int height, bool hasAlpha = false);
cudaError_t SavePlanarFloatImageToPNG(const std::string& filename,
    const float* deviceImageData,
    int width, int height,
    bool hasAlpha,
    cudaStream_t stream);

std::string ConvertWCharToChar(const wchar_t* wcharStr);
std::string ConvertWCharToChar(const std::wstring& wstr);
std::wstring ConvertCharToWChar(const std::string& str);
Ort::Value CreateOrtValueFromDeviceMemory(void* deviceBuffer, const std::vector<int64_t>& shape,
    ONNXTensorElementDataType dataType, const Ort::MemoryInfo& memoryInfo);
bool loadRGBAImageToCudaBufferFloatPlanar(const char* rgbImagePath, const char* alphaMaskPath,
    float* cudaBuffer, int expectedWidth, int expectedHeight,
    cudaStream_t stream);

// String conversion utilities
std::string ConvertWCharToString(const wchar_t* wstr);
std::wstring ConvertStringToWString(const std::string& str);
std::string ConvertWStringToString(const std::wstring& wstr);