#include "BackgroundEstimationKernels.cuh"
#include <cuda_runtime.h>
#include <device_launch_parameters.h>
#include <float.h>

// Constants for background estimation
#define MIN_ALPHA_VALUE 5.0f / 255.0f
#define MAX_ALPHA_VALUE 250.0f / 255.0f
#define MIN_CONFIDENCE 0.01f // Minimum confidence value to prevent division by zero

#ifdef __CUDACC__

// Structure to hold background estimation data (RGB + confidence)
struct BackgroundData {
	float r, g, b;
	float confidence; // Higher values indicate more reliable estimates

	__device__ BackgroundData() : r(0.0f), g(0.0f), b(0.0f), confidence(0.0f) {}
	__device__ BackgroundData(float r_, float g_, float b_, float conf_) : r(r_), g(g_), b(b_), confidence(conf_) {}
};

// Helper function to check if a pixel is complete background
__device__ bool isCompleteBackground(const float* alphaData, int x, int y, int width, int height) {
	// Check if current pixel is background
	if (alphaData[y * width + x] > MIN_ALPHA_VALUE) {
		return false;
	}

	// Check all 8 surrounding pixels
	for (int dy = -1; dy <= 1; dy++) {
		for (int dx = -1; dx <= 1; dx++) {
			if (dx == 0 && dy == 0)
				continue; // Skip center pixel

			int nx = x + dx;
			int ny = y + dy;

			// Check bounds
			if (nx < 0 || nx >= width || ny < 0 || ny >= height) {
				return false; // Consider out-of-bounds as non-background
			}

			// Check if surrounding pixel is background
			if (alphaData[ny * width + nx] > MIN_ALPHA_VALUE) {
				return false;
			}
		}
	}

	return true;
}

// Helper function to calculate color variance in a neighborhood (for confidence estimation)
__device__ float calculateColorVariance(const float* rgbData, int x, int y, int width, int height) {
	int rgbPlaneSize = width * height;
	float sumR = 0.0f, sumG = 0.0f, sumB = 0.0f;
	float sumR2 = 0.0f, sumG2 = 0.0f, sumB2 = 0.0f;
	int validPixels = 0;

	// Sample center pixel and all 8 surrounding pixels
	for (int dy = -1; dy <= 1; dy++) {
		for (int dx = -1; dx <= 1; dx++) {
			int nx = x + dx;
			int ny = y + dy;

			// Check bounds
			if (nx >= 0 && nx < width && ny >= 0 && ny < height) {
				int neighborIdx = ny * width + nx;
				float r = rgbData[neighborIdx];
				float g = rgbData[rgbPlaneSize + neighborIdx];
				float b = rgbData[2 * rgbPlaneSize + neighborIdx];

				sumR += r;
				sumG += g;
				sumB += b;
				sumR2 += r * r;
				sumG2 += g * g;
				sumB2 += b * b;
				validPixels++;
			}
		}
	}

	if (validPixels <= 1)
		return 1.0f; // High variance if insufficient data

	float meanR = sumR / validPixels;
	float meanG = sumG / validPixels;
	float meanB = sumB / validPixels;

	float varR = (sumR2 / validPixels) - (meanR * meanR);
	float varG = (sumG2 / validPixels) - (meanG * meanG);
	float varB = (sumB2 / validPixels) - (meanB * meanB);

	return (varR + varG + varB) / 3.0f; // Average variance across channels
}

// Helper function for linear interpolation between two background pixels with improved confidence
__device__ BackgroundData interpolateBackground(const BackgroundData& left, const BackgroundData& right,
	int leftX, int rightX, int currentX, int maxDimension) {
	if (leftX == rightX) {
		return left;
	}

	float t = (float)(currentX - leftX) / (float)(rightX - leftX);
	int totalDistance = rightX - leftX;
	float maxInterpolationDistance = (float)maxDimension;

	BackgroundData result;
	result.r = left.r + t * (right.r - left.r);
	result.g = left.g + t * (right.g - left.g);
	result.b = left.b + t * (right.b - left.b);

	// Confidence decreases with distance and increases with endpoint confidence
	int distToLeft = currentX - leftX;
	int distToRight = rightX - currentX;
	int minDist = min(distToLeft, distToRight);

	// Distance penalty: closer pixels get higher confidence
	float distancePenalty = fmaxf(0.1f, 1.0f - (float)minDist / maxInterpolationDistance);

	// Endpoint confidence: average the confidence of the two endpoints
	float endpointConfidence = (left.confidence + right.confidence) / 2.0f;

	// Interpolation span penalty: shorter spans are more reliable
	float spanPenalty = fmaxf(0.1f, 1.0f - (float)totalDistance / (2.0f * maxInterpolationDistance));

	result.confidence = endpointConfidence * distancePenalty * spanPenalty;

	return result;
}

// Kernel 1: Horizontal background estimation
__global__ void estimateBackgroundHorizontalKernel(
	const float* alphaData,
	const float* rgbData,
	BackgroundData* horizontalBgData,
	int width,
	int height) {
	int y = blockIdx.y * blockDim.y + threadIdx.y;

	if (y >= height)
		return;

	// Process each horizontal line
	int lastCompleteX = -1;
	BackgroundData lastCompleteBg;
	bool hasAnyComplete = false;
	int maxDimension = max(width, height);

	// First pass: find all complete background pixels and store them
	for (int x = 0; x < width; x++) {
		int pixelIdx = y * width + x;

		if (isCompleteBackground(alphaData, x, y, width, height)) {
			// Found a complete background pixel
			BackgroundData currentBg;
			int rgbPlaneSize = width * height;

			// Calculate average RGB from center pixel and all surrounding pixels
			float sumR = 0.0f, sumG = 0.0f, sumB = 0.0f;
			int validPixels = 0;

			// Sample center pixel and all 8 surrounding pixels
			for (int dy = -1; dy <= 1; dy++) {
				for (int dx = -1; dx <= 1; dx++) {
					int nx = x + dx;
					int ny = y + dy;

					// Check bounds
					if (nx >= 0 && nx < width && ny >= 0 && ny < height) {
						int neighborIdx = ny * width + nx;
						sumR += rgbData[neighborIdx];					 // R plane
						sumG += rgbData[rgbPlaneSize + neighborIdx];	 // G plane
						sumB += rgbData[2 * rgbPlaneSize + neighborIdx]; // B plane
						validPixels++;
					}
				}
			}

			// Use averaged values
			if (validPixels > 0) {
				currentBg.r = sumR / validPixels;
				currentBg.g = sumG / validPixels;
				currentBg.b = sumB / validPixels;
			}
			else {
				// Fallback to center pixel if no valid neighbors (shouldn't happen)
				currentBg.r = rgbData[pixelIdx];
				currentBg.g = rgbData[rgbPlaneSize + pixelIdx];
				currentBg.b = rgbData[2 * rgbPlaneSize + pixelIdx];
			}

			// Calculate confidence based on color consistency
			float colorVariance = calculateColorVariance(rgbData, x, y, width, height);
			currentBg.confidence = fmaxf(MIN_CONFIDENCE, 1.0f - colorVariance); // Lower variance = higher confidence

			if (lastCompleteX >= 0) {
				// Interpolate between last complete and current complete pixel
				for (int interpX = lastCompleteX + 1; interpX < x; interpX++) {
					int interpIdx = y * width + interpX;
					horizontalBgData[interpIdx] = interpolateBackground(lastCompleteBg, currentBg, lastCompleteX, x, interpX, maxDimension);
				}
			}

			// Store current complete background pixel
			horizontalBgData[pixelIdx] = currentBg;
			lastCompleteX = x;
			lastCompleteBg = currentBg;
			hasAnyComplete = true;
		}
	}

	// Handle pixels after the last complete background pixel
	if (hasAnyComplete && lastCompleteX >= 0) {
		for (int x = lastCompleteX + 1; x < width; x++) {
			int pixelIdx = y * width + x;
			BackgroundData extendedBg = lastCompleteBg;
			int distance = x - lastCompleteX;

			// Confidence decreases with distance from last known good pixel
			float distancePenalty = fmaxf(0.05f, 1.0f - (float)distance / (float)maxDimension);
			extendedBg.confidence = lastCompleteBg.confidence * distancePenalty;

			horizontalBgData[pixelIdx] = extendedBg;
		}

		// Handle pixels before the first complete background pixel
		for (int x = 0; x < lastCompleteX && x < width; x++) {
			int pixelIdx = y * width + x;
			if (horizontalBgData[pixelIdx].confidence == 0.0f) { // Not already filled
				BackgroundData extendedBg = lastCompleteBg;
				int distance = lastCompleteX - x;

				float distancePenalty = fmaxf(0.05f, 1.0f - (float)distance / (float)maxDimension);
				extendedBg.confidence = lastCompleteBg.confidence * distancePenalty;

				horizontalBgData[pixelIdx] = extendedBg;
			}
		}
	}
	else {
		// No complete background pixels found in this row - use fallback
		for (int x = 0; x < width; x++) {
			int pixelIdx = y * width + x;
			horizontalBgData[pixelIdx] = BackgroundData(0.0f, 0.0f, 0.0f, MIN_CONFIDENCE);
		}
	}
}

// Kernel 2: Vertical background estimation
__global__ void estimateBackgroundVerticalKernel(
	const float* alphaData,
	const float* rgbData,
	BackgroundData* verticalBgData,
	int width,
	int height) {
	int x = blockIdx.x * blockDim.x + threadIdx.x;

	if (x >= width)
		return;

	// Process each vertical line
	int lastCompleteY = -1;
	BackgroundData lastCompleteBg;
	bool hasAnyComplete = false;
	int maxDimension = max(width, height);

	for (int y = 0; y < height; y++) {
		int pixelIdx = y * width + x;

		if (isCompleteBackground(alphaData, x, y, width, height)) {
			// Found a complete background pixel
			BackgroundData currentBg;
			int rgbPlaneSize = width * height;

			// Calculate average RGB from center pixel and all surrounding pixels
			float sumR = 0.0f, sumG = 0.0f, sumB = 0.0f;
			int validPixels = 0;

			// Sample center pixel and all 8 surrounding pixels
			for (int dy = -1; dy <= 1; dy++) {
				for (int dx = -1; dx <= 1; dx++) {
					int nx = x + dx;
					int ny = y + dy;

					// Check bounds
					if (nx >= 0 && nx < width && ny >= 0 && ny < height) {
						int neighborIdx = ny * width + nx;
						sumR += rgbData[neighborIdx];					 // R plane
						sumG += rgbData[rgbPlaneSize + neighborIdx];	 // G plane
						sumB += rgbData[2 * rgbPlaneSize + neighborIdx]; // B plane
						validPixels++;
					}
				}
			}

			// Use averaged values
			if (validPixels > 0) {
				currentBg.r = sumR / validPixels;
				currentBg.g = sumG / validPixels;
				currentBg.b = sumB / validPixels;
			}
			else {
				// Fallback to center pixel if no valid neighbors (shouldn't happen)
				currentBg.r = rgbData[pixelIdx];
				currentBg.g = rgbData[rgbPlaneSize + pixelIdx];
				currentBg.b = rgbData[2 * rgbPlaneSize + pixelIdx];
			}

			// Calculate confidence based on color consistency
			float colorVariance = calculateColorVariance(rgbData, x, y, width, height);
			currentBg.confidence = fmaxf(MIN_CONFIDENCE, 1.0f - colorVariance);

			if (lastCompleteY >= 0) {
				// Interpolate between last complete and current complete pixel
				for (int interpY = lastCompleteY + 1; interpY < y; interpY++) {
					int interpIdx = interpY * width + x;
					verticalBgData[interpIdx] = interpolateBackground(lastCompleteBg, currentBg, lastCompleteY, y, interpY, maxDimension);
				}
			}

			// Store current complete background pixel
			verticalBgData[pixelIdx] = currentBg;
			lastCompleteY = y;
			lastCompleteBg = currentBg;
			hasAnyComplete = true;
		}
	}

	// Handle pixels after the last complete background pixel
	if (hasAnyComplete && lastCompleteY >= 0) {
		for (int y = lastCompleteY + 1; y < height; y++) {
			int pixelIdx = y * width + x;
			BackgroundData extendedBg = lastCompleteBg;
			int distance = y - lastCompleteY;

			float distancePenalty = fmaxf(0.05f, 1.0f - (float)distance / (float)maxDimension);
			extendedBg.confidence = lastCompleteBg.confidence * distancePenalty;

			verticalBgData[pixelIdx] = extendedBg;
		}

		// Handle pixels before the first complete background pixel
		for (int y = 0; y < lastCompleteY && y < height; y++) {
			int pixelIdx = y * width + x;
			if (verticalBgData[pixelIdx].confidence == 0.0f) { // Not already filled
				BackgroundData extendedBg = lastCompleteBg;
				int distance = lastCompleteY - y;

				float distancePenalty = fmaxf(0.05f, 1.0f - (float)distance / (float)maxDimension);
				extendedBg.confidence = lastCompleteBg.confidence * distancePenalty;

				verticalBgData[pixelIdx] = extendedBg;
			}
		}
	}
	else {
		// No complete background pixels found in this column - use fallback
		for (int y = 0; y < height; y++) {
			int pixelIdx = y * width + x;
			verticalBgData[pixelIdx] = BackgroundData(0.0f, 0.0f, 0.0f, MIN_CONFIDENCE);
		}
	}
}

// Kernel 3: Extract foreground using estimated background and alpha compositing formula
__global__ void extractForegroundKernel(
	const float* alphaData,
	const float* originalRgbData,
	const BackgroundData* horizontalBgData,
	const BackgroundData* verticalBgData,
	float* finalRgbaData,
	int width,
	int height) {
	int x = blockIdx.x * blockDim.x + threadIdx.x;
	int y = blockIdx.y * blockDim.y + threadIdx.y;

	if (x >= width || y >= height)
		return;

	int pixelIdx = y * width + x;
	int rgbaIdx = pixelIdx * 4;
	float alpha = alphaData[pixelIdx];

	// Get original RGB values
	int rgbPlaneSize = width * height;
	float originalR = originalRgbData[pixelIdx];
	float originalG = originalRgbData[rgbPlaneSize + pixelIdx];
	float originalB = originalRgbData[2 * rgbPlaneSize + pixelIdx];

	if (alpha <= MIN_ALPHA_VALUE) {
		// Pure background pixel - output original RGB (which is background)
		finalRgbaData[rgbaIdx + 0] = originalR;
		finalRgbaData[rgbaIdx + 1] = originalG;
		finalRgbaData[rgbaIdx + 2] = originalB;
		finalRgbaData[rgbaIdx + 3] = alpha;
	}
	else if (alpha >= MAX_ALPHA_VALUE) {
		// Pure foreground pixel - output original RGB (which is foreground)
		finalRgbaData[rgbaIdx + 0] = originalR;
		finalRgbaData[rgbaIdx + 1] = originalG;
		finalRgbaData[rgbaIdx + 2] = originalB;
		finalRgbaData[rgbaIdx + 3] = alpha;
	}
	else {
		// Semi-transparent pixel - extract foreground using: foreground = (output - (1-alpha) × background) / alpha
		BackgroundData horizontalBg = horizontalBgData[pixelIdx];
		BackgroundData verticalBg = verticalBgData[pixelIdx];

		// Fixed weighting logic: higher confidence gets more weight
		float totalConfidence = horizontalBg.confidence + verticalBg.confidence;
		float horizontalWeight, verticalWeight;

		if (totalConfidence > MIN_CONFIDENCE) {
			horizontalWeight = horizontalBg.confidence / totalConfidence;
			verticalWeight = verticalBg.confidence / totalConfidence;
		}
		else {
			// Fallback to equal weighting if both have very low confidence
			horizontalWeight = 0.5f;
			verticalWeight = 0.5f;
		}

		float backgroundR = horizontalWeight * horizontalBg.r + verticalWeight * verticalBg.r;
		float backgroundG = horizontalWeight * horizontalBg.g + verticalWeight * verticalBg.g;
		float backgroundB = horizontalWeight * horizontalBg.b + verticalWeight * verticalBg.b;

		// Extract foreground: foreground = (output - (1-alpha) × background) / alpha
		float oneMinusAlpha = 1.0f - alpha;
		float foregroundR = (originalR - oneMinusAlpha * backgroundR) / alpha;
		float foregroundG = (originalG - oneMinusAlpha * backgroundG) / alpha;
		float foregroundB = (originalB - oneMinusAlpha * backgroundB) / alpha;

		// Clamp foreground values to valid range [0, 1]
		foregroundR = fmaxf(0.0f, fminf(1.0f, foregroundR));
		foregroundG = fmaxf(0.0f, fminf(1.0f, foregroundG));
		foregroundB = fmaxf(0.0f, fminf(1.0f, foregroundB));

		finalRgbaData[rgbaIdx + 0] = foregroundR;
		finalRgbaData[rgbaIdx + 1] = foregroundG;
		finalRgbaData[rgbaIdx + 2] = foregroundB;
		finalRgbaData[rgbaIdx + 3] = alpha;
	}
}

// Launcher functions
extern "C" {
void launchEstimateBackgroundHorizontal(
	const float* alphaData,
	const float* rgbData,
	void* horizontalBgData,
	int width,
	int height,
	cudaStream_t stream) {
	dim3 blockSize(1, 16); // Process one line per block
	dim3 gridSize(1, (height + blockSize.y - 1) / blockSize.y);

	estimateBackgroundHorizontalKernel<<<gridSize, blockSize, 0, stream>>>(
		alphaData, rgbData, (BackgroundData*)horizontalBgData, width, height);
}

void launchEstimateBackgroundVertical(
	const float* alphaData,
	const float* rgbData,
	void* verticalBgData,
	int width,
	int height,
	cudaStream_t stream) {
	dim3 blockSize(16, 1); // Process one column per block
	dim3 gridSize((width + blockSize.x - 1) / blockSize.x, 1);

	estimateBackgroundVerticalKernel<<<gridSize, blockSize, 0, stream>>>(
		alphaData, rgbData, (BackgroundData*)verticalBgData, width, height);
}

void launchExtractForeground(
	const float* alphaData,
	const float* originalRgbData,
	const void* horizontalBgData,
	const void* verticalBgData,
	float* finalRgbaData,
	int width,
	int height,
	cudaStream_t stream) {
	dim3 blockSize(16, 16);
	dim3 gridSize((width + blockSize.x - 1) / blockSize.x,
		(height + blockSize.y - 1) / blockSize.y);

	extractForegroundKernel<<<gridSize, blockSize, 0, stream>>>(
		alphaData, originalRgbData,
		(const BackgroundData*)horizontalBgData,
		(const BackgroundData*)verticalBgData,
		finalRgbaData, width, height);
}
}
#endif