﻿  Compiling CUDA source file src\main_Kernels.cu...
  
  F:\Catechese\EditeurAudioVideo\ImageMatter>"C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\bin\nvcc.exe" -gencode=arch=compute_52,code=\"sm_52,compute_52\" --use-local-env -ccbin "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\bin\HostX64\x64" -x cu   -I"C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\\include" -IF:\VccLibs\ffmpeg\include -IF:\VccLibs\onnxruntime\include -Iinclude -I"C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include" -I"C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include"     --keep-dir ImageMatter\x64\Release  -maxrregcount=0    --machine 64 --compile -cudart static --expt-relaxed-constexpr   -DWIN32 -DWIN64 -DNDEBUG -D_CONSOLE -D_MBCS -Xcompiler "/EHsc /W3 /nologo /O2 /FS   /MD " -Xcompiler "/FdImageMatter\x64\Release\vc143.pdb" -o F:\Catechese\EditeurAudioVideo\ImageMatter\ImageMatter\x64\Release\main_Kernels.cu.obj "F:\Catechese\EditeurAudioVideo\ImageMatter\src\main_Kernels.cu" 
CUDACOMPILE : nvcc warning : Support for offline compilation for architectures prior to '<compute/sm/lto>_75' will be removed in a future release (Use -Wno-deprecated-gpu-targets to suppress warning).
  main_Kernels.cu
  tmpxft_00004394_00000000-7_main_Kernels.cudafe1.cpp
  main.cpp
     Création de la bibliothèque F:\Catechese\EditeurAudioVideo\ImageMatter\x64\Release\ImageMatter.lib et de l'objet F:\Catechese\EditeurAudioVideo\ImageMatter\x64\Release\ImageMatter.exp
  Génération de code en cours
LINK : fatal error LNK1257: échec de la génération du code
