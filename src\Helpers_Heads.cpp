#include "Helpers_Heads.h"
#include "Helpers_Kernels.cuh"
#include <algorithm>
#include <iostream>

// Function to generate distinct colors for each detection
void GetDistinctColor(int index, float& r, float& g, float& b) {
    // Predefined distinct colors
    const float colors[][3] = {
        {1.0f, 0.0f, 0.0f},   // Red
        {0.0f, 1.0f, 0.0f},   // Green
        {0.0f, 0.0f, 1.0f},   // Blue
        {1.0f, 1.0f, 0.0f},   // Yellow
        {1.0f, 0.0f, 1.0f},   // Magenta
        {0.0f, 1.0f, 1.0f},   // Cyan
        {1.0f, 0.5f, 0.0f},   // Orange
        {0.5f, 0.0f, 1.0f},   // Purple
        {0.0f, 0.5f, 0.0f},   // Dark Green
        {0.5f, 0.5f, 0.5f},   // Gray
    };

    int colorIndex = index % 10;
    r = colors[colorIndex][0];
    g = colors[colorIndex][1];
    b = colors[colorIndex][2];
}

// Main debug visualization function - uses RGB buffer
bool DrawHeadDetectionDebugOverlay(float* d_rgbBuffer, int width, int height,
    const std::vector<Box>& results) {
    if (!d_rgbBuffer || results.empty()) {
        return false;
    }

    int detectionCount = 0;

    // Draw bounding boxes and labels for each detection
    for (size_t i = 0; i < results.size() && detectionCount < 50; ++i) {
        const auto& result = results[i];

        // Skip if confidence is too low (shouldn't happen if properly filtered, but just in case)
        if (result.confidence < 0.1f) continue;

        // Get distinct color for this detection
        float r, g, b;
        GetDistinctColor(detectionCount, r, g, b);

        // Launch rectangle drawing kernel (assuming it now handles RGB format)
        if (!LaunchDrawRectangleKernel(d_rgbBuffer, width, height,
            static_cast<int>(result.x), static_cast<int>(result.y),
            static_cast<int>(result.x + result.width), static_cast<int>(result.y + result.height),
            r, g, b, 3)) {  // Note: removed alpha parameter, added channel count
            std::cerr << "Failed to launch rectangle drawing kernel" << std::endl;
            return false;
        }

        // Draw detection number at top-left of bounding box
        int labelX = static_cast<int>(result.x) + 5;
        int labelY = static_cast<int>(result.y) + 5;

        // Make sure label fits within image
        if (labelX + 5 < width && labelY + 7 < height) {
            if (!LaunchDrawNumberKernel(d_rgbBuffer, width, height,
                labelX, labelY, detectionCount, r, g, b)) {  // Note: removed alpha parameter
                std::cerr << "Failed to launch number drawing kernel" << std::endl;
                return false;
            }
        }

        detectionCount++;
    }

    // Draw legend in top-right corner
    int legendStartX = width - 200;
    int legendStartY = 20;

    // Draw legend background (black rectangle - no transparency support in RGB)
    if (!LaunchDrawRectangleKernel(d_rgbBuffer, width, height,
        legendStartX - 10, legendStartY - 10,
        width - 10, legendStartY + detectionCount * 20 + 10,
        0.0f, 0.0f, 0.0f, 1, nullptr)) {
        std::cerr << "Failed to launch legend background drawing kernel" << std::endl;
        return false;
    }

    // Draw legend entries
    for (int i = 0; i < detectionCount; ++i) {
        float r, g, b;
        GetDistinctColor(i, r, g, b);

        int entryY = legendStartY + i * 20;

        // Draw color square
        if (!LaunchDrawRectangleKernel(d_rgbBuffer, width, height,
            legendStartX, entryY,
            legendStartX + 15, entryY + 15,
            r, g, b, 15, nullptr)) {
            std::cerr << "Failed to launch color square drawing kernel" << std::endl;
            return false;
        }

        // Draw detection number
        if (!LaunchDrawNumberKernel(d_rgbBuffer, width, height,
            legendStartX + 25, entryY + 4, i,
            1.0f, 1.0f, 1.0f, nullptr)) {
            std::cerr << "Failed to launch legend number drawing kernel" << std::endl;
            return false;
        }

        // Add confidence display (draw as percentage)
        if (i < results.size()) {
            int confidence = (int)(results[i].confidence * 100);
            int tensDigit = confidence / 10;
            int onesDigit = confidence % 10;

            // Draw tens digit
            if (tensDigit > 0) {
                if (!LaunchDrawNumberKernel(d_rgbBuffer, width, height,
                    legendStartX + 40, entryY + 4, tensDigit,
                    1.0f, 1.0f, 1.0f)) {
                    std::cerr << "Failed to launch tens digit drawing kernel" << std::endl;
                    return false;
                }
            }

            // Draw ones digit
            if (!LaunchDrawNumberKernel(d_rgbBuffer, width, height,
                legendStartX + 50, entryY + 4, onesDigit,
                1.0f, 1.0f, 1.0f)) {
                std::cerr << "Failed to launch ones digit drawing kernel" << std::endl;
                return false;
            }
        }
    }

    // Synchronize to ensure all drawing operations complete
    cudaError_t error = cudaDeviceSynchronize();
    if (error != cudaSuccess) {
        std::cerr << "CUDA error in DrawHeadDetectionDebugOverlay: " << cudaGetErrorString(error) << std::endl;
        return false;
    }

    return true;
}